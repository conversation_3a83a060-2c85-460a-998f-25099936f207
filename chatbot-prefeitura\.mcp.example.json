{"mcpServers": {"postgres-prefeitura": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres", "${POSTGRES_CONNECTION_STRING}"], "description": "PostgreSQL MCP para explorar dados estruturados da Prefeitura", "transport": "stdio"}, "mongodb-prefeitura": {"command": "npx", "args": ["mongo-mcp", "${MONGODB_CONNECTION_STRING}"], "description": "MongoDB MCP para dados de conversas e logs do chatbot", "transport": "stdio"}}, "version": "1.0", "description": "MCPs configurados para desenvolvimento do chatbot da Prefeitura", "usage": {"setup": ["1. <PERSON><PERSON> este arquivo para .mcp.json", "2. Substitua ${POSTGRES_CONNECTION_STRING} pela string de conexão real", "3. Substitua ${MONGODB_CONNECTION_STRING} pela string de conexão real", "4. Configure no <PERSON>"], "postgres": ["Liste todas as tabelas do PostgreSQL", "Descreva a estrutura da tabela [nome]", "Quantos registros tem a tabela [nome]?", "Mostre os relacionamentos entre tabelas"], "mongodb": ["Liste as coleções do MongoDB", "Quantos documentos tem cada coleção?", "Mostre um documento de exemplo da coleção [nome]"]}}