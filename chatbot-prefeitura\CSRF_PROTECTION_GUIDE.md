# 🛡️ Guia de Proteção CSRF

## 📋 Visão Geral

Este sistema implementa proteção robusta contra ataques **Cross-Site Request Forgery (CSRF)** usando tokens seguros e verificação de origem. A proteção é aplicada automaticamente em todas as operações que modificam dados.

---

## 🏗️ Arquitetura da Proteção

### Backend (Node.js/Express)

#### 1. **Middleware CSRF Personalizado**
```typescript
// src/middleware/csrf.ts
- Geração de tokens seguros usando crypto
- Verificação timing-safe de tokens
- Logs de segurança para tentativas de ataque
- Suporte a cookies httpOnly
```

#### 2. **<PERSON><PERSON><PERSON> Prote<PERSON>**
```typescript
// Rotas que requerem CSRF:
POST /api/auth/login
POST /api/auth/logout  
POST /api/auth/refresh-token
POST /api/auth/change-password
POST /api/chat/message

// Rotas livres (métodos seguros):
GET /api/csrf-token
GET /api/auth/me
GET /api/chat/history
GET /health
```

### Frontend (React/Next.js)

#### 1. **Cliente CSRF Automático**
```typescript
// src/lib/csrf.ts
- Obtenção automática de tokens
- Cache inteligente com renovação
- Retry automático em caso de token inválido
- Wrappers para métodos HTTP
```

#### 2. **Hooks React**
```typescript
// src/hooks/useCSRF.ts
- useCSRF() - Hook completo com estado
- useCSRFRequest() - Hook simplificado para requisições
- useCSRFStatus() - Monitoramento de status
```

#### 3. **Contexto Global**
```typescript
// src/contexts/CSRFContext.tsx
- Provedor global de CSRF
- Auto-refresh de tokens
- Status visual para desenvolvimento
```

---

## 🚀 Como Usar

### 1. **Configuração Inicial**

#### Backend
```bash
# As dependências já estão instaladas
# Nenhuma configuração adicional necessária
```

#### Frontend
```tsx
// app/layout.tsx
import { CSRFProvider } from '../contexts/CSRFContext';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <CSRFProvider autoRefresh={true}>
          {children}
        </CSRFProvider>
      </body>
    </html>
  );
}
```

### 2. **Fazendo Requisições Seguras**

#### Método 1: Usando o Serviço de API
```tsx
import apiService from '../services/api';

// Login
const response = await apiService.login({
  email: '<EMAIL>',
  password: 'password',
  secretaria: 'administracao'
});

// Enviar mensagem
const response = await apiService.sendMessage({
  message: 'Olá!',
  conversationId: 'conv_123'
});
```

#### Método 2: Usando Hooks
```tsx
import { useCSRFRequest } from '../hooks/useCSRF';

function MyComponent() {
  const { post, isLoading, error } = useCSRFRequest();
  
  const handleSubmit = async (data) => {
    try {
      const response = await post('/api/chat/message', data);
      // Processar resposta...
    } catch (error) {
      console.error('Erro:', error);
    }
  };
}
```

#### Método 3: Usando Cliente Direto
```tsx
import { fetchWithCSRF } from '../lib/csrf';

const response = await fetchWithCSRF('/api/auth/login', {
  method: 'POST',
  body: JSON.stringify(loginData)
});
```

### 3. **Componentes de Exemplo**

#### Formulário de Login
```tsx
import LoginForm from '../components/Auth/LoginForm';

<LoginForm
  onSuccess={(userData) => console.log('Login OK:', userData)}
  onError={(error) => console.error('Erro:', error)}
/>
```

#### Chat
```tsx
import ChatForm from '../components/Chat/ChatForm';

<ChatForm
  conversationId="conv_123"
  onMessageSent={(message) => console.log('Enviado:', message)}
  onError={(error) => console.error('Erro:', error)}
/>
```

---

## 🧪 Testando a Proteção

### 1. **Testes Automatizados**
```bash
# Executar testes de CSRF
cd backend
npm run test:csrf
```

### 2. **Testes Manuais**

#### Teste 1: Obter Token
```bash
curl -X GET http://localhost:3001/api/csrf-token \
  -H "Content-Type: application/json" \
  -c cookies.txt
```

#### Teste 2: Requisição sem Token (deve falhar)
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123","secretaria":"admin"}'
# Esperado: 403 Forbidden
```

#### Teste 3: Requisição com Token (deve passar)
```bash
# Primeiro obter token
TOKEN=$(curl -s -X GET http://localhost:3001/api/csrf-token -c cookies.txt | jq -r '.data.csrfToken')

# Depois usar o token
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $TOKEN" \
  -b cookies.txt \
  -d '{"email":"<EMAIL>","password":"123","secretaria":"admin"}'
# Esperado: 400/401 (credenciais inválidas), não 403
```

---

## 🔧 Configuração Avançada

### 1. **Variáveis de Ambiente**
```env
# .env
CSRF_SECRET=sua-chave-csrf-super-secreta
NODE_ENV=production
FRONTEND_URL=https://seu-dominio.com
```

### 2. **Personalização do Middleware**
```typescript
// Configurar origens permitidas
import { setAllowedOrigins } from '../middleware/originVerification';

setAllowedOrigins([
  'https://seu-dominio.com',
  'https://app.seu-dominio.com'
]);
```

### 3. **Headers de Segurança Adicionais**
```typescript
// Já configurado no helmet
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      // ... outras diretivas
    }
  }
}));
```

---

## 🚨 Troubleshooting

### Problema: "Token CSRF ausente"
**Solução:**
1. Verificar se o CSRFProvider está configurado
2. Verificar se cookies estão habilitados
3. Verificar se a requisição inclui credentials: 'include'

### Problema: "Token CSRF inválido"
**Solução:**
1. Verificar se o token não expirou
2. Verificar se os cookies estão sendo enviados
3. Tentar forçar refresh do token

### Problema: CORS + CSRF
**Solução:**
1. Verificar se X-CSRF-Token está em allowedHeaders
2. Verificar se credentials: true está configurado
3. Verificar se a origem está permitida

---

## 📊 Monitoramento

### 1. **Logs de Segurança**
```bash
# Ver logs de tentativas CSRF
tail -f backend/logs/security.log | grep csrf_violation
```

### 2. **Métricas de Proteção**
- Taxa de bloqueio CSRF
- Tentativas de ataque por IP
- Performance do middleware

### 3. **Alertas**
- Configurar alertas para múltiplas tentativas CSRF
- Monitorar origens suspeitas
- Alertas de falha na geração de tokens

---

## ✅ Checklist de Segurança

- [x] Tokens CSRF implementados
- [x] Verificação de origem ativa
- [x] Logs de segurança configurados
- [x] Testes automatizados criados
- [x] Headers de segurança configurados
- [x] Rate limiting aplicado
- [x] Validação de entrada robusta
- [x] Sanitização XSS ativa

---

## 📚 Recursos Adicionais

- [OWASP CSRF Prevention](https://owasp.org/www-community/attacks/csrf)
- [MDN: CSRF](https://developer.mozilla.org/en-US/docs/Glossary/CSRF)
- [Express Security Best Practices](https://expressjs.com/en/advanced/best-practice-security.html)

---

**🛡️ Sistema totalmente protegido contra ataques CSRF!**
