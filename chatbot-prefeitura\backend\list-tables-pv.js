const { Client } = require('pg');

async function listTablesPv() {
  console.log('📋 Listando tabelas do banco pv_valparaiso...\n');
  
  const client = new Client({
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'pv_valparaiso'
  });

  try {
    await client.connect();
    console.log('✅ Conectado ao pv_valparaiso!\n');

    // Listar tabelas que podem ser relevantes para o sistema
    const relevantTables = await client.query(`
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        AND (
          table_name ILIKE '%usuario%' OR
          table_name ILIKE '%secretaria%' OR
          table_name ILIKE '%funcionario%' OR
          table_name ILIKE '%processo%' OR
          table_name ILIKE '%protocolo%' OR
          table_name ILIKE '%cidadao%' OR
          table_name ILIKE '%admin%' OR
          table_name ILIKE '%auth%' OR
          table_name ILIKE '%login%' OR
          table_name ILIKE '%permiss%' OR
          table_name ILIKE '%role%' OR
          table_name ILIKE '%departamento%' OR
          table_name ILIKE '%setor%'
        )
      ORDER BY table_name;
    `);

    console.log('🎯 Tabelas relevantes encontradas:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    if (relevantTables.rows.length > 0) {
      for (const table of relevantTables.rows) {
        // Contar registros
        try {
          const count = await client.query(`SELECT COUNT(*) FROM "${table.table_name}"`);
          console.log(`📊 ${table.table_name.padEnd(30)} | ${table.column_count.toString().padStart(3)} colunas | ${count.rows[0].count.toString().padStart(8)} registros`);
        } catch (e) {
          console.log(`📊 ${table.table_name.padEnd(30)} | ${table.column_count.toString().padStart(3)} colunas | erro ao contar`);
        }
      }
    } else {
      console.log('❌ Nenhuma tabela relevante encontrada com os critérios de busca');
    }

    // Listar todas as tabelas (primeiras 50)
    console.log('\n📋 Todas as tabelas (primeiras 50):');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const allTables = await client.query(`
      SELECT table_name
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name
      LIMIT 50;
    `);

    allTables.rows.forEach((row, i) => {
      console.log(`${(i + 1).toString().padStart(3)}. ${row.table_name}`);
    });

    if (allTables.rows.length === 50) {
      const totalCount = await client.query(`
        SELECT COUNT(*) as total
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
          AND table_type = 'BASE TABLE'
      `);
      console.log(`\n... e mais ${totalCount.rows[0].total - 50} tabelas`);
    }

    await client.end();
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

listTablesPv().catch(console.error);
