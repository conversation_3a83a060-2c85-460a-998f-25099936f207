{"name": "chatbot-prefeitura-backend", "version": "1.0.0", "description": "Backend API para Sistema de Chatbot da Prefeitura", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src --ext .ts", "typecheck": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^5.13.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.3.2", "morgan": "^1.10.0", "openai": "^4.52.1", "pg": "^8.11.5", "prisma": "^5.13.0", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/morgan": "^1.9.9", "@types/node": "^20.12.7", "@types/pg": "^8.11.5", "@typescript-eslint/eslint-plugin": "^7.7.1", "@typescript-eslint/parser": "^7.7.1", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsx": "^4.7.3", "typescript": "^5.4.5"}}