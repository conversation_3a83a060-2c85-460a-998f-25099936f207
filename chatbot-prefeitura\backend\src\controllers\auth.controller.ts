import { Request, Response, NextFunction } from 'express';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { getAuthService } from '../services/auth/authService';
import { LoginDto, SecretariaKey } from '../types/user.types';
import authLogger from '../utils/authLogger';
import passwordResetService from '../services/auth/passwordResetService';

/**
 * 🔐 Login com sistema híbrido
 * Suporta email ou CPF + senha + secretaria
 */
export const login = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, cpf, password, secretaria } = req.body;

    // Validação básica
    if (!password || !secretaria) {
      throw new AppError('Senha e secretaria são obrigatórios', 400);
    }

    if (!email && !cpf) {
      throw new AppError('Email ou CPF é obrigatório', 400);
    }

    // Montar dados de login
    const loginData: LoginDto = {
      email,
      cpf,
      password,
      secretaria: secretaria as SecretariaKey
    };

    // Usar AuthService híbrido
    const authService = getAuthService();
    const loginResult = await authService.login(loginData);

    // Log baseado no resultado do login
    if (!loginResult.success) {
      // Registrar falha de login com detalhes seguros
      if (loginResult.reason === 'USER_NOT_FOUND') {
        authLogger.logLoginFailedUserNotFound(req, email || cpf);
      } else if (loginResult.reason === 'INVALID_PASSWORD') {
        authLogger.logLoginFailedInvalidPassword(req, {
          userId: loginResult.userId,
          email,
          secretaria: secretaria as SecretariaKey
        });
      } else if (loginResult.reason === 'ACCOUNT_LOCKED') {
        authLogger.logLoginFailedAccountLocked(req, {
          userId: loginResult.userId!,
          email,
          secretaria: secretaria as SecretariaKey
        });
      }
      
      const statusCode = loginResult.reason === 'USER_NOT_FOUND' ? 404 : 401;
      throw new AppError(loginResult.error || 'Falha na autenticação', statusCode);
    }

    // Registrar login bem-sucedido
    authLogger.logLoginSuccess(req, {
      userId: loginResult.user!.userId,
      email: loginResult.user!.email,
      secretaria: loginResult.user!.secretaria,
      role: loginResult.user!.role
    });

    // Obter informações do sistema para logs interno
    const systemInfo = authService.getSystemInfo();
    logger.info(`✅ Login processado para ${loginResult.user!.userId} - Usando: ${systemInfo.usingRealUsers ? 'DADOS REAIS' : 'DADOS MOCK'}`);

    // Resposta padronizada
    res.json({
      status: 'success',
      data: {
        user: loginResult.user,
        token: loginResult.token,
        tokenType: 'Bearer' as const,
        expiresIn: loginResult.expiresIn
      },
      meta: {
        usingRealUsers: systemInfo.usingRealUsers,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Erro no login:', error);
    next(error);
  }
};

/**
 * 🚪 Logout usando AuthService
 */
export const logout = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.userId || req.user?.sub;
    
    if (userId) {
      const authService = getAuthService();
      await authService.logout(userId);
      
      logger.info(`🚪 Logout realizado: ${req.user?.nome || req.user?.email} (ID: ${userId})`);
    }
    
    res.json({
      status: 'success',
      message: 'Logout realizado com sucesso',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Erro no logout:', error);
    next(error);
  }
};

/**
 * 👤 Obter dados do usuário autenticado
 */
export const me = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      throw new AppError('Token não fornecido', 401);
    }

    const authService = getAuthService();
    const user = await authService.getAuthenticatedUser(token);

    if (!user) {
      throw new AppError('Usuário não encontrado ou token inválido', 404);
    }

    res.json({
      status: 'success',
      data: {
        user
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Erro ao obter dados do usuário:', error);
    next(error);
  }
};

/**
 * 🔄 Renovar token JWT
 */
export const refreshToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      throw new AppError('Token não fornecido', 401);
    }

    const authService = getAuthService();
    const newToken = await authService.refreshToken(token);

    if (!newToken) {
      throw new AppError('Token inválido ou expirado', 401);
    }

    logger.info('🔄 Token renovado com sucesso');

    res.json({
      status: 'success',
      data: {
        token: newToken,
        tokenType: 'Bearer' as const
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Erro ao renovar token:', error);
    next(error);
  }
};

/**
 * 🔐 Alterar senha
 */
export const changePassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const userId = req.user?.userId || req.user?.sub;

    if (!oldPassword || !newPassword) {
      throw new AppError('Senha atual e nova senha são obrigatórias', 400);
    }

    if (!userId) {
      throw new AppError('Usuário não autenticado', 401);
    }

    const authService = getAuthService();
    const success = await authService.changePassword(userId, oldPassword, newPassword);

    if (!success) {
      throw new AppError('Senha atual incorreta', 400);
    }

    logger.info(`🔐 Senha alterada para usuário: ${userId}`);

    res.json({
      status: 'success',
      message: 'Senha alterada com sucesso',
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Erro ao alterar senha:', error);
    next(error);
  }
};

/**
 * 📊 Informações do sistema de autenticação
 */
/**
 * 📧 Solicitar reset de senha
 */
export const requestPasswordReset = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent');

    if (!email) {
      throw new AppError('Email é obrigatório', 400);
    }

    // Solicitar reset
    const result = await passwordResetService.requestPasswordReset(
      email,
      ipAddress,
      userAgent || 'unknown'
    );

    if (result.success) {
      // Sempre retornar sucesso para não vazar informações sobre emails existentes
      res.json({
        status: 'success',
        message: 'Se o email estiver cadastrado, você receberá as instruções para redefinir sua senha',
        data: process.env.NODE_ENV === 'development' ? { token: result.token } : {},
        meta: {
          timestamp: new Date().toISOString()
        }
      });
    } else {
      // Em caso de erro interno, ainda retornar sucesso para o usuário
      res.json({
        status: 'success', 
        message: 'Se o email estiver cadastrado, você receberá as instruções para redefinir sua senha',
        meta: {
          timestamp: new Date().toISOString()
        }
      });
    }
  } catch (error) {
    logger.error('Erro ao solicitar reset de senha:', error);
    next(error);
  }
};

/**
 * ✅ Validar token de reset
 */
export const validatePasswordResetToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { token } = req.params;

    if (!token) {
      throw new AppError('Token é obrigatório', 400);
    }

    // Validar token
    const validation = passwordResetService.validateResetToken(token);

    if (validation.valid) {
      res.json({
        status: 'success',
        message: 'Token válido',
        data: {
          email: validation.email?.split('@')[0] + '***@' + validation.email?.split('@')[1]
        },
        meta: {
          timestamp: new Date().toISOString()
        }
      });
    } else {
      let message = 'Token inválido';
      if (validation.expired) message = 'Token expirado';
      if (validation.used) message = 'Token já foi utilizado';

      throw new AppError(message, 400);
    }
  } catch (error) {
    logger.error('Erro ao validar token de reset:', error);
    next(error);
  }
};

/**
 * 🔐 Executar reset de senha
 */
export const executePasswordReset = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { token, newPassword } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent');

    if (!token || !newPassword) {
      throw new AppError('Token e nova senha são obrigatórios', 400);
    }

    // Executar reset
    const result = await passwordResetService.executePasswordReset(
      token,
      newPassword,
      ipAddress,
      userAgent || 'unknown'
    );

    if (result.success) {
      res.json({
        status: 'success',
        message: result.message,
        meta: {
          timestamp: new Date().toISOString()
        }
      });
    } else {
      throw new AppError(result.message, 400);
    }
  } catch (error) {
    logger.error('Erro ao executar reset de senha:', error);
    next(error);
  }
};

export const systemInfo = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authService = getAuthService();
    const info = authService.getSystemInfo();
    const resetStats = passwordResetService.getResetStats();
    
    res.json({
      status: 'success',
      data: {
        usingRealUsers: info.usingRealUsers,
        userCount: await info.userCount,
        version: info.version,
        passwordReset: {
          activeTokens: resetStats.activeTokens,
          totalTokensGenerated: resetStats.totalTokens
        }
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Erro ao obter informações do sistema:', error);
    next(error);
  }
};