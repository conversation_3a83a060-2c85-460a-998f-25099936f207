import { Request, Response, NextFunction } from 'express';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { chatService } from '../services/chatbot/chatService';

export const sendMessage = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { message, conversationId } = req.body;
    const { userId, secretaria } = req.user!;

    logger.info(`Nova mensagem de ${userId} - ${secretaria}: ${message.substring(0, 50)}...`);

    const response = await chatService.processMessage({
      message,
      conversationId,
      userId,
      secretaria
    });

    res.json({
      status: 'success',
      data: response
    });
  } catch (error) {
    next(error);
  }
};

export const getHistory = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { conversationId } = req.params;
    const { userId, secretaria, role } = req.user!;

    // Aplicar isolamento baseado no role
    let filter: any = { secretaria };
    
    if (role === 'admin') {
      // Admin pode ver tudo - não aplicar filtro de secretaria
      filter = {};
    } else if (role === 'gestor') {
      // Gestor vê apenas da sua secretaria
      filter = { secretaria };
    } else {
      // Operador e consulta veem apenas suas próprias conversas
      filter = { userId, secretaria };
    }

    logger.info(`Histórico solicitado por ${userId} (${role}) da ${secretaria} com filtros:`, filter);

    const history = await chatService.getConversationHistory({
      conversationId,
      ...filter
    });

    res.json({
      status: 'success',
      data: history
    });
  } catch (error) {
    next(error);
  }
};