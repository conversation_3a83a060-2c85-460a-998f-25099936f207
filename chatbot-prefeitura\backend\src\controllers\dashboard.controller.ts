import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export const getMetrics = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { secretaria, role, userId } = req.user!;

    // Mock de métricas - será substituído por consultas reais ao banco
    // Aplicar isolamento baseado no role
    let metrics: any = {};
    
    if (role === 'admin') {
      // Admin vê métricas globais
      metrics = {
        totalProcessos: 1245,
        processosEmAndamento: 387,
        processosConcluidos: 858,
        tempoMedioResposta: '2.1 dias',
        satisfacaoUsuarios: 89,
        consultasHoje: 234,
        secretarias: 7,
        usuariosAtivos: 89
      };
    } else if (role === 'gestor') {
      // Gestor vê métricas da secretaria
      metrics = {
        totalProcessos: 245,
        processosEmAndamento: 87,
        processosConcluidos: 158,
        tempoMedioResposta: '2.5 dias',
        satisfacaoUsuarios: 87,
        consultasHoje: 34,
        secretaria: secretaria
      };
    }

    logger.info(`Métricas ${role === 'admin' ? 'globais' : 'da ' + secretaria} solicitadas por ${userId} (${role})`);

    res.json({
      status: 'success',
      data: metrics
    });
  } catch (error) {
    next(error);
  }
};

export const getRecentActivities = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { secretaria, role, userId } = req.user!;

    // Mock de atividades - será substituído por consultas reais ao banco
    let activities: any[] = [];
    
    if (role === 'admin') {
      // Admin vê atividades de todas as secretarias
      activities = [
        {
          id: '1',
          tipo: 'processo',
          descricao: 'Novo processo cadastrado #2024001',
          data: new Date().toISOString(),
          usuario: 'João Silva',
          secretaria: 'administracao'
        },
        {
          id: '2', 
          tipo: 'consulta',
          descricao: 'Consulta sobre orçamento realizada',
          data: new Date(Date.now() - 3600000).toISOString(),
          usuario: 'Maria Santos',
          secretaria: 'financas'
        },
        {
          id: '3',
          tipo: 'usuario',
          descricao: 'Novo usuário cadastrado na Saúde',
          data: new Date(Date.now() - 7200000).toISOString(),
          usuario: 'Carlos Lima',
          secretaria: 'saude'
        }
      ];
    } else if (role === 'gestor') {
      // Gestor vê apenas da sua secretaria
      activities = [
        {
          id: '1',
          tipo: 'processo',
          descricao: `Novo processo cadastrado #2024001 - ${secretaria}`,
          data: new Date().toISOString(),
          usuario: 'João Silva',
          secretaria: secretaria
        },
        {
          id: '2',
          tipo: 'consulta', 
          descricao: `Consulta realizada na ${secretaria}`,
          data: new Date(Date.now() - 3600000).toISOString(),
          usuario: 'Maria Santos',
          secretaria: secretaria
        }
      ];
    }

    logger.info(`Atividades recentes ${role === 'admin' ? 'globais' : 'da ' + secretaria} solicitadas por ${userId} (${role})`);

    res.json({
      status: 'success',
      data: activities
    });
  } catch (error) {
    next(error);
  }
};