import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { errorHandler } from './middleware/errorHandler';
import { logger, securityLogger } from './utils/logger';
import authRoutes from './routes/auth.routes';
import chatRoutes from './routes/chat.routes';
import dashboardRoutes from './routes/dashboard.routes';
import { validateEnv } from './config/env.validator';
import rateLimiters from './middleware/rateLimiter';

dotenv.config();

// Validar variáveis de ambiente obrigatórias
try {
  validateEnv();
} catch (error) {
  logger.error('Falha na validação das variáveis de ambiente:', error);
  process.exit(1);
}

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware com configuração robusta
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'", "https:", "data:"],
      connectSrc: ["'self'"],
      mediaSrc: ["'self'"],
      objectSrc: ["'none'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: { policy: "same-origin-allow-popups" },
  crossOriginResourcePolicy: { policy: "cross-origin" },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token']
}));

// Rate limiting global - proteção básica contra DDoS
app.use(rateLimiters.general);

// Body parsing
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging com informações de segurança
app.use(morgan('combined', { 
  stream: { 
    write: message => {
      logger.http(message.trim());
      
      // Log de segurança para requisições suspeitas
      if (message.includes('429') || message.includes('4')) {
        securityLogger.warn('Suspicious HTTP request', {
          logEntry: message.trim(),
          timestamp: new Date().toISOString()
        });
      }
    }
  }
}));

// Middleware para logar tentativas suspeitas
app.use((req, res, next) => {
  const suspiciousPatterns = [
    /\/\.\./,  // Directory traversal
    /<script/i, // XSS attempts  
    /union.*select/i, // SQL injection
    /drop.*table/i, // SQL injection
    /exec\(/i, // Code injection
  ];
  
  const fullUrl = `${req.method} ${req.originalUrl}`;
  const body = JSON.stringify(req.body);
  
  if (suspiciousPatterns.some(pattern => 
    pattern.test(fullUrl) || pattern.test(body)
  )) {
    securityLogger.warn('Suspicious request detected', {
      ip: req.ip,
      method: req.method,
      url: req.originalUrl,
      userAgent: req.get('User-Agent'),
      body: req.body,
      headers: req.headers,
      timestamp: new Date().toISOString()
    });
  }
  
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Ping endpoint (sem logs para não poluir)
app.get('/ping', (req, res) => {
  res.json({ pong: true });
});

// CSRF Token endpoint
import { generateCSRFToken, getCSRFToken } from './middleware/csrf';
app.get('/api/csrf-token', generateCSRFToken, getCSRFToken);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/dashboard', dashboardRoutes);

// Error handling com logs de segurança
app.use((error: any, req: any, res: any, next: any) => {
  // Log erros de segurança
  if (error.status === 429 || error.type === 'validation') {
    securityLogger.warn('Security-related error', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }
  
  errorHandler(error, req, res, next);
});

// Start server
app.listen(PORT, () => {
  logger.info(`🚀 Server running on port ${PORT}`);
  logger.info(`🛡️ Security features enabled: Rate limiting, Helmet, CORS, Input validation, CSRF protection`);
  logger.info(`📋 Environment: ${process.env.NODE_ENV || 'development'}`);

  // Log de segurança do início do servidor
  securityLogger.info('Server started', {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
    security_features: [
      'rate_limiting',
      'helmet_headers',
      'cors_protection',
      'input_validation',
      'xss_protection',
      'sql_injection_protection',
      'csrf_protection'
    ]
  });
});