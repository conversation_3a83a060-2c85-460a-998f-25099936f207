import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { sanitizeAuditEvent, sanitizeHeaders } from '../utils/logSanitizer';

interface AuditEvent {
  action: string;
  userId?: string;
  secretaria?: string;
  role?: string;
  resource: string;
  method: string;
  ip: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
  additionalData?: any;
}

/**
 * Middleware de auditoria para registrar acessos a recursos sensíveis
 * @param action Ação sendo realizada (ex: 'access_metrics', 'view_history')
 * @returns Middleware function
 */
export const auditAccess = (action: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    // Capturar dados da requisição
    const auditData: Partial<AuditEvent> = {
      action,
      userId: req.user?.userId,
      secretaria: req.user?.secretaria,
      role: req.user?.role,
      resource: req.originalUrl,
      method: req.method,
      ip: req.ip || req.connection.remoteAddress || 'unknown',
      userAgent: req.get('User-Agent')
    };

    // Interceptar a resposta para determinar sucesso/erro
    const originalSend = res.send;
    let responseBody: any;
    
    res.send = function(data: any) {
      responseBody = data;
      return originalSend.call(this, data);
    };

    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const success = res.statusCode < 400;
      
      const rawAuditData: AuditEvent = {
        ...auditData as AuditEvent,
        success,
        errorMessage: !success ? `HTTP ${res.statusCode}` : undefined,
        additionalData: {
          statusCode: res.statusCode,
          duration: `${duration}ms`,
          timestamp: new Date().toISOString()
        }
      };

      // Sanitizar dados de auditoria antes de logar
      const finalAuditData = sanitizeAuditEvent(rawAuditData);

      // Log de auditoria com diferentes níveis baseado no resultado
      if (success) {
        logger.info(`[AUDIT] ${action} - User: ${auditData.userId} (${auditData.role}) from ${auditData.secretaria}`, finalAuditData);
      } else {
        logger.warn(`[AUDIT] FAILED ${action} - User: ${auditData.userId} (${auditData.role}) from ${auditData.secretaria}`, finalAuditData);
      }

      // Log específico de segurança para ações críticas
      if (action.includes('admin') || action.includes('system') || !success) {
        logger.audit({
          type: 'access_audit',
          severity: success ? 'info' : 'warning',
          details: finalAuditData
        });
      }
    });

    next();
  };
};

/**
 * Middleware específico para auditar tentativas de acesso não autorizado
 */
export const auditUnauthorized = (req: Request, res: Response, next: NextFunction) => {
  const auditData = {
    action: 'unauthorized_access_attempt',
    resource: req.originalUrl,
    method: req.method,
    ip: req.ip || 'unknown',
    userAgent: req.get('User-Agent'),
    headers: req.headers,
    body: req.method === 'POST' ? req.body : undefined,
    timestamp: new Date().toISOString()
  };

  logger.warn('[SECURITY] Unauthorized access attempt', auditData);
  
  // Log específico de segurança
  logger.audit({
    type: 'unauthorized_access',
    severity: 'high',
    ip: req.ip || 'unknown',
    details: auditData
  });

  next();
};

/**
 * Middleware para auditar alterações de dados sensíveis
 */
export const auditDataChange = (dataType: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const auditData = {
      action: `modify_${dataType}`,
      userId: req.user?.userId,
      secretaria: req.user?.secretaria,
      role: req.user?.role,
      resource: req.originalUrl,
      method: req.method,
      ip: req.ip || 'unknown',
      dataType,
      changes: req.body,
      timestamp: new Date().toISOString()
    };

    logger.info(`[AUDIT] Data modification attempt - ${dataType}`, auditData);
    
    // Log específico para alterações de dados
    logger.audit({
      type: 'data_modification',
      severity: 'medium',
      details: auditData
    });

    next();
  };
};

export default {
  auditAccess,
  auditUnauthorized,
  auditDataChange
};