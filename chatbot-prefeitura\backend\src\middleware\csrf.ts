import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { logger, logSecurityEvent } from '../utils/logger';

interface CSRFConfig {
  secret: string;
  cookieName: string;
  headerName: string;
  tokenLength: number;
  maxAge: number;
  secure: boolean;
  sameSite: 'strict' | 'lax' | 'none';
}

class CSRFProtection {
  private config: CSRFConfig;

  constructor() {
    this.config = {
      secret: process.env.CSRF_SECRET || process.env.JWT_SECRET || 'default-csrf-secret',
      cookieName: 'csrf-token',
      headerName: 'x-csrf-token',
      tokenLength: 32,
      maxAge: 3600000, // 1 hora
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    };
  }

  /**
   * 🔐 Gerar token CSRF seguro
   */
  private generateToken(): string {
    return crypto.randomBytes(this.config.tokenLength).toString('hex');
  }

  /**
   * 🔒 Criar hash do token para verificação
   */
  private createTokenHash(token: string, secret: string): string {
    return crypto
      .createHmac('sha256', secret)
      .update(token)
      .digest('hex');
  }

  /**
   * ✅ Verificar se o token é válido
   */
  private verifyToken(token: string, hash: string): boolean {
    try {
      const expectedHash = this.createTokenHash(token, this.config.secret);
      return crypto.timingSafeEqual(
        Buffer.from(hash, 'hex'),
        Buffer.from(expectedHash, 'hex')
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * 🎫 Middleware para gerar e fornecer token CSRF
   */
  generateCSRFToken = (req: Request, res: Response, next: NextFunction): void => {
    try {
      const token = this.generateToken();
      const hash = this.createTokenHash(token, this.config.secret);

      // Armazenar hash na sessão/cookie seguro
      res.cookie(`${this.config.cookieName}-hash`, hash, {
        httpOnly: true,
        secure: this.config.secure,
        sameSite: this.config.sameSite,
        maxAge: this.config.maxAge,
        path: '/'
      });

      // Adicionar token ao request para uso posterior
      (req as any).csrfToken = token;

      next();
    } catch (error) {
      logger.error('Erro ao gerar token CSRF:', error);
      res.status(500).json({
        status: 'error',
        message: 'Erro interno do servidor',
        code: 'CSRF_GENERATION_ERROR'
      });
    }
  };

  /**
   * 🍪 Extrair cookies do header manualmente
   */
  private parseCookies(req: Request): Record<string, string> {
    const cookies: Record<string, string> = {};
    const cookieHeader = req.get('Cookie');

    if (cookieHeader) {
      cookieHeader.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name && value) {
          cookies[name] = decodeURIComponent(value);
        }
      });
    }

    return cookies;
  }

  /**
   * 🛡️ Middleware para verificar token CSRF
   */
  verifyCSRFToken = (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Métodos seguros não precisam de verificação CSRF
      if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
        return next();
      }

      const token = req.get(this.config.headerName) || req.body._csrf;
      const cookies = this.parseCookies(req);
      const hash = cookies[`${this.config.cookieName}-hash`];

      if (!token || !hash) {
        this.logCSRFViolation(req, 'Token ou hash ausente');
        return res.status(403).json({
          status: 'error',
          message: 'Token CSRF ausente',
          code: 'CSRF_TOKEN_MISSING'
        });
      }

      if (!this.verifyToken(token, hash)) {
        this.logCSRFViolation(req, 'Token inválido');
        return res.status(403).json({
          status: 'error',
          message: 'Token CSRF inválido',
          code: 'CSRF_TOKEN_INVALID'
        });
      }

      // Token válido, continuar
      next();
    } catch (error) {
      logger.error('Erro na verificação CSRF:', error);
      res.status(500).json({
        status: 'error',
        message: 'Erro interno do servidor',
        code: 'CSRF_VERIFICATION_ERROR'
      });
    }
  };

  /**
   * 📊 Log de violações CSRF
   */
  private logCSRFViolation(req: Request, reason: string): void {
    const cookies = this.parseCookies(req);

    logSecurityEvent({
      type: 'csrf_violation',
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      details: {
        reason,
        endpoint: req.originalUrl,
        method: req.method,
        headers: {
          origin: req.get('Origin'),
          referer: req.get('Referer'),
          'x-csrf-token': req.get(this.config.headerName) ? '[PRESENT]' : '[MISSING]'
        },
        cookies: {
          'csrf-hash': cookies[`${this.config.cookieName}-hash`] ? '[PRESENT]' : '[MISSING]'
        }
      },
      severity: 'high'
    });
  }

  /**
   * 🎯 Endpoint para obter token CSRF
   */
  getCSRFToken = (req: Request, res: Response): void => {
    const token = (req as any).csrfToken;
    
    if (!token) {
      return res.status(500).json({
        status: 'error',
        message: 'Token CSRF não foi gerado',
        code: 'CSRF_TOKEN_NOT_GENERATED'
      });
    }

    res.json({
      status: 'success',
      data: {
        csrfToken: token,
        headerName: this.config.headerName
      },
      message: 'Token CSRF gerado com sucesso'
    });
  };

  /**
   * 🔄 Middleware para renovar token CSRF
   */
  renewCSRFToken = (req: Request, res: Response, next: NextFunction): void => {
    // Gerar novo token após operações críticas
    this.generateCSRFToken(req, res, next);
  };
}

// Instância singleton
const csrfProtection = new CSRFProtection();

// Exportar middlewares
export const generateCSRFToken = csrfProtection.generateCSRFToken;
export const verifyCSRFToken = csrfProtection.verifyCSRFToken;
export const getCSRFToken = csrfProtection.getCSRFToken;
export const renewCSRFToken = csrfProtection.renewCSRFToken;

// Middleware combinado para rotas que precisam gerar e verificar
export const csrfProtection = [generateCSRFToken, verifyCSRFToken];

export default csrfProtection;
