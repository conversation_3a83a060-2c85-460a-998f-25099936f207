import { Request, Response, NextFunction } from 'express';
import { logger, logSecurityEvent } from '../utils/logger';

interface OriginConfig {
  allowedOrigins: string[];
  allowLocalhost: boolean;
  strictMode: boolean;
}

class OriginVerification {
  private config: OriginConfig;

  constructor() {
    this.config = {
      allowedOrigins: [
        process.env.FRONTEND_URL || 'http://localhost:3000',
        'http://localhost:3000',
        'https://localhost:3000'
      ],
      allowLocalhost: process.env.NODE_ENV === 'development',
      strictMode: process.env.NODE_ENV === 'production'
    };

    // Adicionar origens do ambiente se existirem
    if (process.env.ALLOWED_ORIGINS) {
      const envOrigins = process.env.ALLOWED_ORIGINS.split(',').map(o => o.trim());
      this.config.allowedOrigins.push(...envOrigins);
    }
  }

  /**
   * 🔍 Verificar se a origem é permitida
   */
  private isOriginAllowed(origin: string | undefined): boolean {
    if (!origin) {
      // Em modo estrito, origem é obrigatória
      return !this.config.strictMode;
    }

    // Verificar origens explicitamente permitidas
    if (this.config.allowedOrigins.includes(origin)) {
      return true;
    }

    // Permitir localhost em desenvolvimento
    if (this.config.allowLocalhost && this.isLocalhostOrigin(origin)) {
      return true;
    }

    return false;
  }

  /**
   * 🏠 Verificar se é origem localhost
   */
  private isLocalhostOrigin(origin: string): boolean {
    const localhostPatterns = [
      /^https?:\/\/localhost(:\d+)?$/,
      /^https?:\/\/127\.0\.0\.1(:\d+)?$/,
      /^https?:\/\/\[::1\](:\d+)?$/
    ];

    return localhostPatterns.some(pattern => pattern.test(origin));
  }

  /**
   * 🛡️ Middleware de verificação de origem
   */
  verifyOrigin = (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Métodos seguros podem passar sem verificação rigorosa
      if (['GET', 'HEAD', 'OPTIONS'].includes(req.method) && !this.config.strictMode) {
        return next();
      }

      const origin = req.get('Origin');
      const referer = req.get('Referer');

      // Usar Origin primeiro, depois Referer como fallback
      const sourceOrigin = origin || (referer ? new URL(referer).origin : undefined);

      if (!this.isOriginAllowed(sourceOrigin)) {
        this.logOriginViolation(req, sourceOrigin);
        
        return res.status(403).json({
          status: 'error',
          message: 'Origem não autorizada',
          code: 'INVALID_ORIGIN'
        });
      }

      // Origem válida, continuar
      next();
    } catch (error) {
      logger.error('Erro na verificação de origem:', error);
      
      if (this.config.strictMode) {
        return res.status(403).json({
          status: 'error',
          message: 'Erro na verificação de origem',
          code: 'ORIGIN_VERIFICATION_ERROR'
        });
      }
      
      // Em modo não-estrito, permitir continuar
      next();
    }
  };

  /**
   * 📊 Log de violações de origem
   */
  private logOriginViolation(req: Request, suspiciousOrigin: string | undefined): void {
    logSecurityEvent({
      type: 'origin_violation',
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      details: {
        suspiciousOrigin: suspiciousOrigin || '[MISSING]',
        allowedOrigins: this.config.allowedOrigins,
        endpoint: req.originalUrl,
        method: req.method,
        headers: {
          origin: req.get('Origin') || '[MISSING]',
          referer: req.get('Referer') || '[MISSING]',
          host: req.get('Host') || '[MISSING]'
        }
      },
      severity: 'medium'
    });
  }

  /**
   * ⚙️ Configurar origens permitidas dinamicamente
   */
  setAllowedOrigins(origins: string[]): void {
    this.config.allowedOrigins = [...origins];
    logger.info('Origens permitidas atualizadas:', { origins });
  }

  /**
   * 📋 Obter configuração atual
   */
  getConfig(): OriginConfig {
    return { ...this.config };
  }
}

// Instância singleton
const originVerification = new OriginVerification();

// Exportar middleware
export const verifyOrigin = originVerification.verifyOrigin;
export const setAllowedOrigins = originVerification.setAllowedOrigins.bind(originVerification);
export const getOriginConfig = originVerification.getConfig.bind(originVerification);

export default originVerification;
