import rateLimit from 'express-rate-limit';
import { Request } from 'express';
import { logSecurityEvent } from '../utils/logger';

/**
 * 🛡️ Rate Limiters para Segurança
 */

/**
 * 🔐 Rate limiter para rotas de autenticação
 * Previne ataques de força bruta
 */
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 tentativas por IP
  message: {
    status: 'error',
    message: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
    code: 'TOO_MANY_AUTH_ATTEMPTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Não contar tentativas bem-sucedidas
  keyGenerator: (req: Request) => {
    // Usar IP + User-Agent para identificação mais precisa
    return `${req.ip}-${req.get('User-Agent') || 'unknown'}`;
  },
  handler: (req: Request, res: any) => {
    // Log de segurança quando limite é atingido
    logSecurityEvent({
      type: 'rate_limit',
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      details: {
        endpoint: '/auth',
        limit: 5,
        window: '15min'
      },
      severity: 'medium'
    });
    
    res.status(429).json({
      status: 'error',
      message: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
      code: 'TOO_MANY_AUTH_ATTEMPTS'
    });
  }
});

/**
 * 💬 Rate limiter para chat
 * Previne spam de mensagens
 */
export const chatRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minuto
  max: 30, // máximo 30 mensagens por minuto
  message: {
    status: 'error',
    message: 'Muitas mensagens enviadas. Aguarde um momento antes de enviar novamente.',
    code: 'TOO_MANY_MESSAGES'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    // Usar ID do usuário se autenticado, senão IP
    return req.user?.userId || req.ip || 'unknown';
  }
});

/**
 * 📊 Rate limiter para dashboard/relatórios
 * Previne sobrecarga em consultas pesadas
 */
export const dashboardRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutos
  max: 50, // máximo 50 requisições por 5 minutos
  message: {
    status: 'error',
    message: 'Muitas consultas ao dashboard. Aguarde alguns minutos.',
    code: 'TOO_MANY_DASHBOARD_REQUESTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.user?.userId || req.ip || 'unknown';
  }
});

/**
 * 🔄 Rate limiter mais restritivo para refresh token
 */
export const refreshTokenRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minuto
  max: 3, // máximo 3 refresh por minuto
  message: {
    status: 'error',
    message: 'Muitas tentativas de renovação de token. Aguarde um momento.',
    code: 'TOO_MANY_REFRESH_ATTEMPTS'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 📤 Rate limiter para exportação de dados
 * Previne sobrecarga em operações custosas
 */
export const exportRateLimit = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutos
  max: 3, // máximo 3 exportações por 10 minutos
  message: {
    status: 'error',
    message: 'Limite de exportações atingido. Aguarde 10 minutos.',
    code: 'TOO_MANY_EXPORT_REQUESTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.user?.userId || req.ip || 'unknown';
  }
});

/**
 * 🌍 Rate limiter geral para todas as rotas
 * Proteção básica contra DDoS
 */
export const generalRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minuto
  max: 100, // máximo 100 requisições por minuto por IP
  message: {
    status: 'error',
    message: 'Muitas requisições. Tente novamente em alguns segundos.',
    code: 'TOO_MANY_REQUESTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req: Request) => {
    // Pular rate limiting para health checks
    return req.path === '/health' || req.path === '/ping';
  }
});

/**
 * 🔍 Rate limiter para busca/pesquisa
 * Previne abuse de endpoints de busca
 */
export const searchRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minuto
  max: 20, // máximo 20 buscas por minuto
  message: {
    status: 'error',
    message: 'Muitas pesquisas realizadas. Aguarde um momento.',
    code: 'TOO_MANY_SEARCH_REQUESTS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.user?.userId || req.ip || 'unknown';
  }
});

/**
 * 🚨 Rate limiter para ações administrativas
 * Extra proteção para operações sensíveis
 */
export const adminRateLimit = rateLimit({
  windowMs: 30 * 60 * 1000, // 30 minutos
  max: 10, // máximo 10 ações administrativas por 30 minutos
  message: {
    status: 'error',
    message: 'Limite de ações administrativas atingido. Aguarde 30 minutos.',
    code: 'TOO_MANY_ADMIN_ACTIONS'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.user?.userId || req.ip || 'unknown';
  }
});

/**
 * 🔐 Rate limiter progressivo para tentativas de login falhadas
 * Aumenta o tempo de bloqueio a cada tentativa
 */
export const progressiveAuthRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hora
  max: 10, // máximo 10 tentativas por hora
  message: {
    status: 'error',
    message: 'Conta temporariamente bloqueada devido a muitas tentativas de login. Aguarde 1 hora.',
    code: 'ACCOUNT_TEMPORARILY_BLOCKED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true,
  keyGenerator: (req: Request) => {
    // Usar email/CPF se fornecido, senão IP
    const { email, cpf } = req.body;
    return email || cpf || req.ip || 'unknown';
  }
});

export default {
  auth: authRateLimit,
  chat: chatRateLimit,
  dashboard: dashboardRateLimit,
  refreshToken: refreshTokenRateLimit,
  export: exportRateLimit,
  general: generalRateLimit,
  search: searchRateLimit,
  admin: adminRateLimit,
  progressiveAuth: progressiveAuthRateLimit
};
