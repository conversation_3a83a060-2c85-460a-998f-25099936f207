import { Request, Response, NextFunction } from 'express';
import { AnyZodObject, ZodError } from 'zod';
import { logSecurityEvent } from '../utils/logger';

export const validateRequest = (schema: AnyZodObject) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params,
      });
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        // Log tentativas com dados inválidos (possível ataque)
        const hasHtmlTags = JSON.stringify(req.body).includes('<');
        const hasScriptTags = JSON.stringify(req.body).toLowerCase().includes('script');
        const hasSqlKeywords = /\b(select|insert|update|delete|drop|union)\b/i.test(JSON.stringify(req.body));
        
        if (hasHtmlTags || hasScriptTags || hasSqlKeywords) {
          logSecurityEvent({
            type: 'validation_failed',
            ip: req.ip || 'unknown',
            userAgent: req.get('User-Agent'),
            details: {
              endpoint: req.originalUrl,
              method: req.method,
              errors: error.errors,
              suspiciousContent: {
                hasHtmlTags,
                hasScriptTags,
                hasSqlKeywords
              },
              body: req.body
            },
            severity: hasScriptTags || hasSqlKeywords ? 'high' : 'medium'
          });
        }
        
        res.status(400).json({
          status: 'error',
          message: 'Dados inválidos',
          code: 'VALIDATION_ERROR',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
        return;
      }
      next(error);
    }
  };
};