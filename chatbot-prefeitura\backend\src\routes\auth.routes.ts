import { Router } from 'express';
import { validateRequest } from '../middleware/validateRequest';
import {
  login,
  logout,
  me,
  refreshToken,
  changePassword,
  systemInfo,
  requestPasswordReset,
  validatePasswordResetToken,
  executePasswordReset
} from '../controllers/auth.controller';
import { authenticate } from '../middleware/auth';
import {
  loginSchema,
  changePasswordSchema,
  refreshTokenSchema,
  systemInfoSchema,
  requestPasswordResetSchema,
  validateResetTokenSchema,
  executePasswordResetSchema
} from '../schemas/auth.schemas';
import rateLimiters from '../middleware/rateLimiter';
import { authorize } from '../middleware/auth';
import { auditAccess } from '../middleware/audit';
import { verifyCSRFToken } from '../middleware/csrf';

const router = Router();

// 🔐 Rotas de autenticação
router.post('/login',
  rateLimiters.auth,
  rateLimiters.progressiveAuth,
  verifyCSRFToken,
  validateRequest(loginSchema),
  login
);

router.post('/logout',
  rateLimiters.auth,
  verifyCSRFToken,
  authenticate,
  logout
);

router.get('/me',
  rateLimiters.general,
  authenticate,
  me
);

// 🔄 Gestão de tokens
router.post('/refresh-token',
  rateLimiters.refreshToken,
  verifyCSRFToken,
  validateRequest(refreshTokenSchema),
  refreshToken
);

// 🔐 Gestão de senhas
router.post('/change-password',
  authenticate,
  rateLimiters.auth,
  verifyCSRFToken,
  validateRequest(changePasswordSchema),
  changePassword
);

// 📊 Informações do sistema (apenas para administradores)
router.get('/system-info', 
  authenticate,
  authorize('admin'),
  auditAccess('access_system_info'),
  rateLimiters.admin,
  validateRequest(systemInfoSchema),
  systemInfo
);

// 🔄 Rotas de reset de senha
router.post('/request-password-reset',
  rateLimiters.auth,
  verifyCSRFToken,
  validateRequest(requestPasswordResetSchema),
  requestPasswordReset
);

router.get('/validate-reset-token/:token',
  rateLimiters.general,
  validateRequest(validateResetTokenSchema),
  validatePasswordResetToken
);

router.post('/reset-password',
  rateLimiters.auth,
  verifyCSRFToken,
  validateRequest(executePasswordResetSchema),
  executePasswordReset
);

export default router;