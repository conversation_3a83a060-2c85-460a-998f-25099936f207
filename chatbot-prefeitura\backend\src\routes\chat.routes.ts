import { Router } from 'express';
import { validateRequest } from '../middleware/validateRequest';
import { sendMessage, getHistory } from '../controllers/chat.controller';
import { authenticate, authorize } from '../middleware/auth';
import {
  sendMessageSchema,
  getHistorySchema
} from '../schemas/chat.schemas';
import rateLimiters from '../middleware/rateLimiter';
import { verifyCSRFToken } from '../middleware/csrf';

const router = Router();

router.use(authenticate);

// 💬 Rotas de chat com validação robusta e rate limiting
router.post('/message',
  rateLimiters.chat,
  verifyCSRFToken,
  validateRequest(sendMessageSchema),
  sendMessage
);

router.get('/history/:conversationId?',
  rateLimiters.chat,
  validateRequest(getHistorySchema),
  getHistory
);

export default router;