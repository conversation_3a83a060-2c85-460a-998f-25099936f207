import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth';
import { validateRequest } from '../middleware/validateRequest';
import { getMetrics, getRecentActivities } from '../controllers/dashboard.controller';
import { 
  getMetricsSchema, 
  getRecentActivitiesSchema 
} from '../schemas/dashboard.schemas';
import rateLimiters from '../middleware/rateLimiter';
import { auditAccess } from '../middleware/audit';

const router = Router();

router.use(authenticate);
router.use(rateLimiters.dashboard);

// 📊 Rotas do dashboard com validação, rate limiting, autorização e auditoria
router.get('/metrics', 
  authorize('admin', 'gestor'),
  auditAccess('access_metrics'),
  validateRequest(getMetricsSchema),
  getMetrics
);

router.get('/activities', 
  authorize('admin', 'gestor'),
  auditAccess('access_activities'),
  validateRequest(getRecentActivitiesSchema),
  getRecentActivities
);

export default router;