import { z } from 'zod';
import { validateCPF, cleanCPF } from '../utils/validators';

/**
 * 🏛️ Secretarias válidas do sistema municipal
 */
const SECRETARIAS_VALIDAS = [
  'administracao',
  'financas', 
  'saude',
  'educacao',
  'obras',
  'social',
  'meio_ambiente'
] as const;

/**
 * 🔐 Schema robusto para login
 * Inclui validações de CPF, email, força de senha e sanitização
 */
export const loginSchema = z.object({
  body: z.object({
    email: z.string()
      .email('Email inválido')
      .max(100, 'Email muito longo (máximo 100 caracteres)')
      .transform(val => val.toLowerCase().trim())
      .refine(email => {
        // Verificar se domínio não contém caracteres suspeitos
        const domain = email.split('@')[1];
        return domain && !/[<>;"'()]/.test(domain);
      }, 'Email contém caracteres inválidos')
      .optional(),
    
    cpf: z.string()
      .min(11, 'CPF deve ter 11 dígitos')
      .max(14, 'CPF muito longo')
      .regex(/^[\d.-]+$/, 'CPF deve conter apenas números, pontos e hífens')
      .transform(cleanCPF) // Remove formatação
      .refine(validateCPF, 'CPF inválido')
      .optional(),
    
    password: z.string()
      .min(8, 'Senha deve ter no mínimo 8 caracteres')
      .max(128, 'Senha muito longa (máximo 128 caracteres)')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        'Senha deve conter: maiúscula, minúscula, número e caractere especial (@$!%*?&)'
      )
      .refine(password => {
        // Verificar senhas comuns
        const commonPasswords = ['password', '123456', 'admin', 'senha123'];
        return !commonPasswords.includes(password.toLowerCase());
      }, 'Senha muito comum, escolha uma mais segura'),
    
    secretaria: z.enum(SECRETARIAS_VALIDAS, {
      errorMap: () => ({ message: 'Secretaria inválida' })
    })
      .transform(val => val.toLowerCase().trim())
  }).refine(data => data.email || data.cpf, {
    message: 'Email ou CPF é obrigatório'
  })
});

/**
 * 🔄 Schema para alteração de senha
 */
export const changePasswordSchema = z.object({
  body: z.object({
    oldPassword: z.string()
      .min(1, 'Senha atual é obrigatória')
      .max(128, 'Senha atual muito longa'),
    
    newPassword: z.string()
      .min(8, 'Nova senha deve ter no mínimo 8 caracteres')
      .max(128, 'Nova senha muito longa (máximo 128 caracteres)')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        'Nova senha deve conter: maiúscula, minúscula, número e caractere especial'
      )
      .refine(password => {
        const commonPasswords = ['password', '123456', 'admin', 'senha123'];
        return !commonPasswords.includes(password.toLowerCase());
      }, 'Nova senha muito comum, escolha uma mais segura')
  }).refine(data => data.oldPassword !== data.newPassword, {
    message: 'Nova senha deve ser diferente da atual',
    path: ['newPassword']
  })
});

/**
 * 🔍 Schema para refresh token
 */
export const refreshTokenSchema = z.object({
  headers: z.object({
    authorization: z.string()
      .min(1, 'Token de autorização é obrigatório')
      .regex(/^Bearer\s+/, 'Token deve começar com "Bearer "')
      .transform(val => val.replace(/^Bearer\s+/, ''))
      .refine(token => {
        // Verificar se o token parece um JWT válido
        const parts = token.split('.');
        return parts.length === 3;
      }, 'Token JWT inválido')
  })
});

/**
 * 📊 Schema para informações do sistema (admin apenas)
 */
export const systemInfoSchema = z.object({
  query: z.object({
    detailed: z.string()
      .optional()
      .transform(val => val === 'true')
  }).optional()
});

/**
 * 📧 Schema para solicitação de reset de senha
 */
export const requestPasswordResetSchema = z.object({
  body: z.object({
    email: z.string()
      .min(1, 'Email é obrigatório')
      .email('Formato de email inválido')
      .max(100, 'Email muito longo')
      .toLowerCase()
      .trim()
  })
});

/**
 * ✅ Schema para validação de token de reset
 */
export const validateResetTokenSchema = z.object({
  params: z.object({
    token: z.string()
      .min(1, 'Token é obrigatório')
      .min(64, 'Token muito curto')
      .max(64, 'Token muito longo')
      .regex(/^[a-f0-9]{64}$/, 'Token deve conter apenas caracteres hexadecimais')
  })
});

/**
 * 🔐 Schema para execução de reset de senha
 */
export const executePasswordResetSchema = z.object({
  body: z.object({
    token: z.string()
      .min(1, 'Token é obrigatório')
      .min(64, 'Token muito curto')
      .max(64, 'Token muito longo')
      .regex(/^[a-f0-9]{64}$/, 'Token deve conter apenas caracteres hexadecimais'),
    
    newPassword: z.string()
      .min(8, 'Senha deve ter pelo menos 8 caracteres')
      .max(128, 'Senha muito longa')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        'Senha deve conter pelo menos: 1 letra minúscula, 1 maiúscula, 1 número e 1 caractere especial (@$!%*?&)'
      )
      .refine(password => {
        const commonPasswords = ['password', '123456', 'admin', 'senha123', 'qwerty', 'abc123', 'password123', '12345678'];
        return !commonPasswords.includes(password.toLowerCase());
      }, 'Nova senha muito comum, escolha uma mais segura')
  })
});

export type LoginInput = z.infer<typeof loginSchema>['body'];
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>['body'];
export type RefreshTokenInput = z.infer<typeof refreshTokenSchema>['headers'];
export type SystemInfoQuery = z.infer<typeof systemInfoSchema>['query'];
export type RequestPasswordResetInput = z.infer<typeof requestPasswordResetSchema>['body'];
export type ValidateResetTokenParams = z.infer<typeof validateResetTokenSchema>['params'];
export type ExecutePasswordResetInput = z.infer<typeof executePasswordResetSchema>['body'];
