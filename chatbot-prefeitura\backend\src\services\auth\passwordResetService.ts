import crypto from 'crypto';
import { logger } from '../../utils/logger';
import authLogger from '../../utils/authLogger';

/**
 * 🔄 Serviço de Reset de Senha
 * Implementa reset seguro por email com tokens únicos
 */

interface PasswordResetToken {
  token: string;
  userId: string;
  email: string;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

// Armazenamento em memória para desenvolvimento
// Em produção, usar Redis ou banco de dados
const passwordResetTokens = new Map<string, PasswordResetToken>();

/**
 * Gera token de reset seguro
 */
const generateResetToken = (): string => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * 📧 Solicitar reset de senha
 */
export const requestPasswordReset = async (
  email: string, 
  ipAddress: string, 
  userAgent: string
): Promise<{ success: boolean; message: string; token?: string }> => {
  try {
    // TODO: Em produção, verificar se email existe no banco
    // Por enquanto, aceitar qualquer email para desenvolvimento
    const userId = `user_${crypto.randomUUID()}`;
    
    // Gerar token único
    const token = generateResetToken();
    
    // Expirar em 1 hora
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 1);
    
    // Armazenar token
    const resetToken: PasswordResetToken = {
      token,
      userId,
      email: email.toLowerCase().trim(),
      expiresAt,
      used: false,
      createdAt: new Date(),
      ipAddress,
      userAgent
    };
    
    passwordResetTokens.set(token, resetToken);
    
    // Limpar tokens expirados (cleanup)
    cleanupExpiredTokens();
    
    // Log de segurança
    logger.info(`[PASSWORD_RESET] Token gerado para ${email.split('@')[0]}***@${email.split('@')[1]}`, {
      email: email.split('@')[0] + '***@' + email.split('@')[1],
      ipAddress,
      userAgent,
      expiresAt,
      tokenLength: token.length
    });
    
    // TODO: Em produção, enviar email real
    // Por enquanto, retornar token para desenvolvimento
    const mockEmailSent = await sendPasswordResetEmail(email, token);
    
    if (mockEmailSent) {
      return {
        success: true,
        message: 'Email de reset enviado com sucesso',
        token: process.env.NODE_ENV === 'development' ? token : undefined // Apenas em dev
      };
    }
    
    return {
      success: false,
      message: 'Erro ao enviar email de reset'
    };
    
  } catch (error) {
    logger.error('Erro ao solicitar reset de senha:', error);
    return {
      success: false,
      message: 'Erro interno do servidor'
    };
  }
};

/**
 * ✅ Validar token de reset
 */
export const validateResetToken = (token: string): { 
  valid: boolean; 
  expired?: boolean; 
  used?: boolean; 
  email?: string;
  userId?: string;
} => {
  const resetToken = passwordResetTokens.get(token);
  
  if (!resetToken) {
    logger.warn('[PASSWORD_RESET] Token não encontrado:', { token: token.slice(0, 8) + '...' });
    return { valid: false };
  }
  
  // Verificar se foi usado
  if (resetToken.used) {
    logger.warn('[PASSWORD_RESET] Tentativa de reutilizar token:', { 
      token: token.slice(0, 8) + '...',
      email: resetToken.email.split('@')[0] + '***@' + resetToken.email.split('@')[1]
    });
    return { valid: false, used: true };
  }
  
  // Verificar expiração
  if (new Date() > resetToken.expiresAt) {
    logger.warn('[PASSWORD_RESET] Token expirado:', { 
      token: token.slice(0, 8) + '...',
      email: resetToken.email.split('@')[0] + '***@' + resetToken.email.split('@')[1],
      expiresAt: resetToken.expiresAt
    });
    return { valid: false, expired: true };
  }
  
  return { 
    valid: true, 
    email: resetToken.email,
    userId: resetToken.userId
  };
};

/**
 * 🔐 Executar reset de senha
 */
export const executePasswordReset = async (
  token: string, 
  newPassword: string,
  ipAddress: string,
  userAgent: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // Validar token
    const tokenValidation = validateResetToken(token);
    
    if (!tokenValidation.valid) {
      if (tokenValidation.expired) {
        return { success: false, message: 'Token de reset expirado' };
      }
      if (tokenValidation.used) {
        return { success: false, message: 'Token de reset já foi utilizado' };
      }
      return { success: false, message: 'Token de reset inválido' };
    }
    
    // Obter dados do token
    const resetToken = passwordResetTokens.get(token)!;
    
    // Validar força da nova senha (usar mesmo schema do sistema)
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.valid) {
      return { 
        success: false, 
        message: `Nova senha não atende aos critérios: ${passwordValidation.errors.join(', ')}`
      };
    }
    
    // TODO: Em produção, atualizar senha no banco de dados
    // const bcrypt = require('bcryptjs');
    // const hashedPassword = await bcrypt.hash(newPassword, 10);
    // await updateUserPassword(resetToken.userId, hashedPassword);
    
    // Marcar token como usado
    resetToken.used = true;
    
    // Log de segurança
    logger.info(`[PASSWORD_RESET] Senha alterada com sucesso`, {
      userId: resetToken.userId,
      email: resetToken.email.split('@')[0] + '***@' + resetToken.email.split('@')[1],
      ipAddress,
      userAgent,
      tokenUsedAt: new Date().toISOString()
    });
    
    // TODO: Em produção, enviar email de confirmação
    await sendPasswordChangedNotification(resetToken.email);
    
    return {
      success: true,
      message: 'Senha alterada com sucesso'
    };
    
  } catch (error) {
    logger.error('Erro ao executar reset de senha:', error);
    return {
      success: false,
      message: 'Erro interno do servidor'
    };
  }
};

/**
 * 📧 Enviar email de reset (MOCK para desenvolvimento)
 */
const sendPasswordResetEmail = async (email: string, token: string): Promise<boolean> => {
  // TODO: Integrar com serviço de email real (SendGrid, AWS SES, etc.)
  
  const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${token}`;
  
  logger.info(`[EMAIL_MOCK] Email de reset enviado`, {
    to: email.split('@')[0] + '***@' + email.split('@')[1],
    resetUrl: resetUrl.replace(token, token.slice(0, 8) + '...'),
    expiresIn: '1 hora'
  });
  
  // Simular envio bem-sucedido
  return true;
};

/**
 * 📧 Enviar notificação de senha alterada
 */
const sendPasswordChangedNotification = async (email: string): Promise<boolean> => {
  logger.info(`[EMAIL_MOCK] Notificação de senha alterada enviada`, {
    to: email.split('@')[0] + '***@' + email.split('@')[1],
    timestamp: new Date().toISOString()
  });
  
  return true;
};

/**
 * 🧹 Limpar tokens expirados
 */
const cleanupExpiredTokens = (): void => {
  const now = new Date();
  let cleaned = 0;
  
  for (const [token, resetToken] of passwordResetTokens.entries()) {
    if (now > resetToken.expiresAt || resetToken.used) {
      passwordResetTokens.delete(token);
      cleaned++;
    }
  }
  
  if (cleaned > 0) {
    logger.info(`[PASSWORD_RESET] ${cleaned} tokens expirados removidos`);
  }
};

/**
 * 💪 Validar força da senha
 */
const validatePasswordStrength = (password: string): { 
  valid: boolean; 
  errors: string[] 
} => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('mínimo 8 caracteres');
  }
  
  if (password.length > 128) {
    errors.push('máximo 128 caracteres');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('pelo menos 1 letra minúscula');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('pelo menos 1 letra maiúscula');
  }
  
  if (!/\d/.test(password)) {
    errors.push('pelo menos 1 número');
  }
  
  if (!/[@$!%*?&]/.test(password)) {
    errors.push('pelo menos 1 caractere especial (@$!%*?&)');
  }
  
  // Blacklist de senhas comuns
  const commonPasswords = [
    'password', '123456', 'admin', 'senha123',
    'qwerty', 'abc123', 'password123', '12345678'
  ];
  
  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('senha muito comum');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * 📊 Obter estatísticas de reset
 */
export const getResetStats = () => {
  const stats = {
    activeTokens: 0,
    expiredTokens: 0,
    usedTokens: 0,
    totalTokens: passwordResetTokens.size
  };
  
  const now = new Date();
  
  for (const resetToken of passwordResetTokens.values()) {
    if (resetToken.used) {
      stats.usedTokens++;
    } else if (now > resetToken.expiresAt) {
      stats.expiredTokens++;
    } else {
      stats.activeTokens++;
    }
  }
  
  return stats;
};

// Auto-cleanup a cada 10 minutos
setInterval(cleanupExpiredTokens, 10 * 60 * 1000);

export default {
  requestPasswordReset,
  validateResetToken,
  executePasswordReset,
  getResetStats
};