import { Request } from 'express';
import { logger, securityLogger } from './logger';
import { sanitizeLogData } from './logSanitizer';

/**
 * 🔐 Sistema de logging específico para eventos de autenticação
 */

export interface AuthEventData {
  userId?: string;
  email?: string;
  secretaria?: string;
  role?: string;
  ip: string;
  userAgent?: string;
  sessionId?: string;
  timestamp: string;
  additionalData?: any;
}

/**
 * Extrai dados seguros da requisição para logging
 */
const extractSafeRequestData = (req: Request): Partial<AuthEventData> => {
  return {
    ip: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
    sessionId: req.sessionID || 'unknown'
  };
};

/**
 * 🟢 Login bem-sucedido
 */
export const logLoginSuccess = (req: Request, userData: { 
  userId: string; 
  email?: string; 
  secretaria: string; 
  role: string 
}) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: userData.userId,
    email: userData.email ? userData.email.split('@')[0] + '@***' : undefined,
    secretaria: userData.secretaria,
    role: userData.role
  };

  logger.info(`[AUTH] LOGIN_SUCCESS: User ${userData.userId} (${userData.role}) from ${userData.secretaria} logged in successfully`, eventData);
  
  securityLogger.info('Login Success', {
    type: 'LOGIN_SUCCESS',
    severity: 'info',
    ...eventData
  });
};

/**
 * 🔴 Login falhado - usuário não encontrado
 */
export const logLoginFailedUserNotFound = (req: Request, identifier: string) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    // Mascarar identificador (email ou CPF)
    email: identifier.includes('@') ? identifier.split('@')[0] + '@***' : undefined,
    additionalData: {
      reason: 'USER_NOT_FOUND',
      identifier: identifier.length > 5 ? identifier.slice(0, 3) + '***' : '***'
    }
  };

  logger.warn(`[AUTH] LOGIN_FAILED_USER_NOT_FOUND: Attempt with identifier ${identifier.slice(0, 3)}***`, eventData);
  
  securityLogger.warn('Login Failed - User Not Found', {
    type: 'LOGIN_FAILED_USER_NOT_FOUND',
    severity: 'medium',
    ...eventData
  });
};

/**
 * 🔴 Login falhado - senha inválida
 */
export const logLoginFailedInvalidPassword = (req: Request, userData: {
  userId?: string;
  email?: string;
  secretaria?: string;
}) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: userData.userId,
    email: userData.email ? userData.email.split('@')[0] + '@***' : undefined,
    secretaria: userData.secretaria,
    additionalData: {
      reason: 'INVALID_PASSWORD'
    }
  };

  logger.warn(`[AUTH] LOGIN_FAILED_INVALID_PASSWORD: Invalid password for user ${userData.userId}`, eventData);
  
  securityLogger.warn('Login Failed - Invalid Password', {
    type: 'LOGIN_FAILED_INVALID_PASSWORD', 
    severity: 'medium',
    ...eventData
  });
};

/**
 * 🔴 Login falhado - conta bloqueada
 */
export const logLoginFailedAccountLocked = (req: Request, userData: {
  userId: string;
  email?: string;
  secretaria?: string;
}) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: userData.userId,
    email: userData.email ? userData.email.split('@')[0] + '@***' : undefined,
    secretaria: userData.secretaria,
    additionalData: {
      reason: 'ACCOUNT_LOCKED'
    }
  };

  logger.warn(`[AUTH] LOGIN_FAILED_ACCOUNT_LOCKED: Attempt on locked account ${userData.userId}`, eventData);
  
  securityLogger.warn('Login Failed - Account Locked', {
    type: 'LOGIN_FAILED_ACCOUNT_LOCKED',
    severity: 'high',
    ...eventData
  });
};

/**
 * 🟡 Logout
 */
export const logLogout = (req: Request, userData: {
  userId: string;
  secretaria: string;
  role: string;
}) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: userData.userId,
    secretaria: userData.secretaria,
    role: userData.role
  };

  logger.info(`[AUTH] LOGOUT: User ${userData.userId} logged out`, eventData);
  
  securityLogger.info('Logout', {
    type: 'LOGOUT',
    severity: 'info',
    ...eventData
  });
};

/**
 * 🔄 Token renovado
 */
export const logTokenRefresh = (req: Request, userData: {
  userId: string;
  secretaria: string;
  role: string;
}) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: userData.userId,
    secretaria: userData.secretaria,
    role: userData.role
  };

  logger.info(`[AUTH] TOKEN_REFRESHED: Token refreshed for user ${userData.userId}`, eventData);
  
  securityLogger.info('Token Refreshed', {
    type: 'TOKEN_REFRESHED',
    severity: 'info',
    ...eventData
  });
};

/**
 * 🔐 Alteração de senha
 */
export const logPasswordChanged = (req: Request, userData: {
  userId: string;
  secretaria: string;
  role: string;
}) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: userData.userId,
    secretaria: userData.secretaria,
    role: userData.role
  };

  logger.info(`[AUTH] PASSWORD_CHANGED: Password changed for user ${userData.userId}`, eventData);
  
  securityLogger.warn('Password Changed', {
    type: 'PASSWORD_CHANGED',
    severity: 'medium',
    ...eventData
  });
};

/**
 * 🚫 Tentativa de acesso com token expirado
 */
export const logTokenExpiredAccess = (req: Request, tokenData?: any) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: tokenData?.userId,
    secretaria: tokenData?.secretaria,
    additionalData: {
      reason: 'TOKEN_EXPIRED',
      resource: req.originalUrl,
      method: req.method
    }
  };

  logger.warn(`[AUTH] TOKEN_EXPIRED_ACCESS: Attempt with expired token on ${req.originalUrl}`, eventData);
  
  securityLogger.warn('Token Expired Access Attempt', {
    type: 'TOKEN_EXPIRED_ACCESS',
    severity: 'medium',
    ...eventData
  });
};

/**
 * 🚫 Tentativa de acesso não autorizado (role inadequado)
 */
export const logUnauthorizedAccess = (req: Request, userData: {
  userId: string;
  role: string;
  secretaria: string;
}, requiredRoles: string[]) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: userData.userId,
    role: userData.role,
    secretaria: userData.secretaria,
    additionalData: {
      reason: 'INSUFFICIENT_PRIVILEGES',
      resource: req.originalUrl,
      method: req.method,
      requiredRoles,
      userRole: userData.role
    }
  };

  logger.warn(`[AUTH] UNAUTHORIZED_ACCESS: User ${userData.userId} (${userData.role}) attempted access to ${req.originalUrl} requiring ${requiredRoles.join('|')}`, eventData);
  
  securityLogger.warn('Unauthorized Access Attempt', {
    type: 'UNAUTHORIZED_ACCESS',
    severity: 'high',
    ...eventData
  });
};

/**
 * 📊 Acesso a recurso administrativo
 */
export const logAdminResourceAccess = (req: Request, userData: {
  userId: string;
  role: string;
  secretaria: string;
}, resource: string) => {
  const eventData: AuthEventData = {
    ...extractSafeRequestData(req),
    userId: userData.userId,
    role: userData.role,
    secretaria: userData.secretaria,
    additionalData: {
      resource,
      method: req.method,
      fullUrl: req.originalUrl
    }
  };

  logger.info(`[AUTH] ADMIN_RESOURCE_ACCESS: Admin ${userData.userId} accessed ${resource}`, eventData);
  
  securityLogger.info('Admin Resource Access', {
    type: 'ADMIN_RESOURCE_ACCESS',
    severity: 'info',
    ...eventData
  });
};

/**
 * 🔍 Múltiplas tentativas de login do mesmo IP
 */
export const logMultipleLoginAttempts = (ip: string, attempts: number, timeWindow: string) => {
  const eventData = {
    ip,
    attempts,
    timeWindow,
    timestamp: new Date().toISOString(),
    additionalData: {
      reason: 'MULTIPLE_FAILED_ATTEMPTS'
    }
  };

  logger.warn(`[AUTH] MULTIPLE_LOGIN_ATTEMPTS: ${attempts} failed attempts from IP ${ip} in ${timeWindow}`, eventData);
  
  securityLogger.warn('Multiple Login Attempts', {
    type: 'MULTIPLE_LOGIN_ATTEMPTS',
    severity: 'high',
    ...eventData
  });
};

export default {
  logLoginSuccess,
  logLoginFailedUserNotFound,
  logLoginFailedInvalidPassword,
  logLoginFailedAccountLocked,
  logLogout,
  logTokenRefresh,
  logPasswordChanged,
  logTokenExpiredAccess,
  logUnauthorizedAccess,
  logAdminResourceAccess,
  logMultipleLoginAttempts
};