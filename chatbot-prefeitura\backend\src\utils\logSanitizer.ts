/**
 * 🧽 Utilitários para sanitização de logs
 * Remove/oculta informações sensíveis antes de gravar nos logs
 */

interface SensitiveData {
  [key: string]: any;
}

/**
 * Lista de campos considerados sensíveis que devem ser redatados
 */
const SENSITIVE_FIELDS = [
  // Credenciais
  'password',
  'senha',
  'oldPassword',
  'newPassword',
  'currentPassword',
  'token',
  'accessToken',
  'refreshToken',
  'jwt',
  'authorization',
  'auth',
  
  // Dados pessoais (LGPD)
  'cpf',
  'rg',
  'cnpj',
  'social_security',
  'passport',
  'credit_card',
  'card_number',
  'cvv',
  
  // Headers sensíveis
  'x-auth-token',
  'x-api-key',
  'cookie',
  'set-cookie',
  
  // Outros dados sensíveis
  'secret',
  'key',
  'private',
  'confidential'
];

/**
 * Padrões regex para detectar dados sensíveis em strings
 */
const SENSITIVE_PATTERNS = [
  // CPF: 123.456.789-01 ou 12345678901
  /\d{3}\.?\d{3}\.?\d{3}-?\d{2}/g,
  
  // CNPJ: 12.345.678/0001-01 ou 12345678000101  
  /\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2}/g,
  
  // JWT Token
  /eyJ[a-zA-Z0-9_-]*\.eyJ[a-zA-Z0-9_-]*\.[a-zA-Z0-9_-]*/g,
  
  // Bearer Token
  /Bearer\s+[a-zA-Z0-9_-]+/gi,
  
  // API Key patterns
  /[a-z0-9]{32,}/gi,
  
  // Email completo (manter apenas domínio)
  /([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g
];

/**
 * Sanitiza um objeto removendo/ocultando campos sensíveis
 */
export const sanitizeObject = (obj: SensitiveData, depth: number = 0): SensitiveData => {
  if (!obj || typeof obj !== 'object' || depth > 5) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, depth + 1));
  }

  const sanitized: SensitiveData = {};

  for (const [key, value] of Object.entries(obj)) {
    const lowerKey = key.toLowerCase();
    
    // Verificar se o campo é sensível
    if (SENSITIVE_FIELDS.some(field => lowerKey.includes(field))) {
      sanitized[key] = '[REDACTED]';
    }
    // Sanitizar objetos aninhados
    else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeObject(value, depth + 1);
    }
    // Sanitizar strings com padrões sensíveis
    else if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    }
    // Manter outros tipos de dados
    else {
      sanitized[key] = value;
    }
  }

  return sanitized;
};

/**
 * Sanitiza strings removendo padrões sensíveis
 */
export const sanitizeString = (str: string): string => {
  if (!str || typeof str !== 'string') {
    return str;
  }

  let sanitized = str;

  // Aplicar padrões de sanitização
  SENSITIVE_PATTERNS.forEach(pattern => {
    if (pattern.source.includes('eyJ')) {
      // JWT - manter apenas início
      sanitized = sanitized.replace(pattern, 'eyJ...[REDACTED_JWT]');
    } else if (pattern.source.includes('Bearer')) {
      // Bearer Token
      sanitized = sanitized.replace(pattern, 'Bearer [REDACTED]');
    } else if (pattern.source.includes('\\d{3}')) {
      // CPF - manter apenas os 3 primeiros dígitos
      sanitized = sanitized.replace(pattern, (match) => {
        const digits = match.replace(/\D/g, '');
        return `${digits.slice(0, 3)}.***.***-**`;
      });
    } else if (pattern.source.includes('\\d{2}')) {
      // CNPJ - manter apenas os 2 primeiros dígitos
      sanitized = sanitized.replace(pattern, (match) => {
        const digits = match.replace(/\D/g, '');
        return `${digits.slice(0, 2)}.***.***/****-**`;
      });
    } else if (pattern.source.includes('@')) {
      // Email - manter apenas o domínio
      sanitized = sanitized.replace(pattern, (match, user, domain) => {
        const maskedUser = user.length > 2 ? `${user.slice(0, 2)}***` : '***';
        return `${maskedUser}@${domain}`;
      });
    } else {
      // Outros padrões
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    }
  });

  return sanitized;
};

/**
 * Sanitiza cabeçalhos HTTP removendo informações sensíveis
 */
export const sanitizeHeaders = (headers: { [key: string]: any }): { [key: string]: any } => {
  const sanitized: { [key: string]: any } = {};

  for (const [key, value] of Object.entries(headers)) {
    const lowerKey = key.toLowerCase();
    
    if (lowerKey.includes('authorization') || lowerKey.includes('cookie') || lowerKey.includes('token')) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
};

/**
 * Sanitiza parâmetros de query string
 */
export const sanitizeQuery = (query: { [key: string]: any }): { [key: string]: any } => {
  return sanitizeObject(query);
};

/**
 * Sanitiza URL removendo parâmetros sensíveis
 */
export const sanitizeUrl = (url: string): string => {
  if (!url) return url;
  
  try {
    const urlObj = new URL(url, 'http://localhost');
    const params = new URLSearchParams(urlObj.search);
    
    // Lista de parâmetros sensíveis comuns
    const sensitiveParams = ['token', 'key', 'password', 'auth', 'secret'];
    
    sensitiveParams.forEach(param => {
      if (params.has(param)) {
        params.set(param, '[REDACTED]');
      }
    });
    
    return `${urlObj.pathname}${params.toString() ? '?' + params.toString() : ''}`;
  } catch {
    return sanitizeString(url);
  }
};

/**
 * Função principal para sanitizar dados de log
 */
export const sanitizeLogData = (data: any): any => {
  if (typeof data === 'string') {
    return sanitizeString(data);
  }
  
  if (typeof data === 'object' && data !== null) {
    return sanitizeObject(data);
  }
  
  return data;
};

/**
 * Sanitiza um evento de auditoria completo
 */
export const sanitizeAuditEvent = (event: any): any => {
  const sanitized = { ...event };
  
  // Sanitizar campos específicos de auditoria
  if (sanitized.details) {
    sanitized.details = sanitizeObject(sanitized.details);
  }
  
  if (sanitized.body) {
    sanitized.body = sanitizeObject(sanitized.body);
  }
  
  if (sanitized.headers) {
    sanitized.headers = sanitizeHeaders(sanitized.headers);
  }
  
  if (sanitized.url) {
    sanitized.url = sanitizeUrl(sanitized.url);
  }
  
  return sanitized;
};

export default {
  sanitizeObject,
  sanitizeString,
  sanitizeHeaders,
  sanitizeQuery,
  sanitizeUrl,
  sanitizeLogData,
  sanitizeAuditEvent
};