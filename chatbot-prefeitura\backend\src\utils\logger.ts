import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { sanitizeLogData } from './logSanitizer';

const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

winston.addColors(colors);

const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf((info) => {
    // Sanitizar dados antes de logar
    const sanitizedMessage = typeof info.message === 'string' 
      ? info.message 
      : JSON.stringify(sanitizeLogData(info.message));
    
    let output = `${info.timestamp} [${info.level}]: ${sanitizedMessage}`;
    
    // Adicionar stack trace para erros (já sanitizado)
    if (info.stack) {
      output += `\n${info.stack}`;
    }
    
    return output;
  }),
  winston.format.colorize({ all: true })
);

// 🔄 Transport com rotação diária para logs gerais
const dailyRotateTransport = new DailyRotateFile({
  filename: 'logs/app-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  maxSize: '100m',
  maxFiles: '30d',
  auditFile: 'logs/.audit-app.json'
});

// 🔄 Transport com rotação diária para erros
const errorRotateTransport = new DailyRotateFile({
  filename: 'logs/error-%DATE%.log', 
  datePattern: 'YYYY-MM-DD',
  level: 'error',
  maxSize: '100m',
  maxFiles: '60d', // Manter erros por mais tempo
  auditFile: 'logs/.audit-error.json'
});

const transports = [
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }),
  dailyRotateTransport,
  errorRotateTransport
];

export const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
});

/**
 * 🛡️ Logger específico para eventos de segurança com sanitização
 */
export const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.printf((info) => {
      // Sanitizar dados de segurança antes de logar
      const sanitizedInfo = sanitizeLogData(info);
      return `${info.timestamp} [SECURITY-${info.level.toUpperCase()}]: ${JSON.stringify(sanitizedInfo)}`;
    })
  ),
  transports: [
    // Transport com rotação para alertas de segurança críticos
    new DailyRotateFile({
      filename: 'logs/security-critical-%DATE%.log',
      datePattern: 'YYYY-MM-DD', 
      level: 'warn',
      maxSize: '50m',
      maxFiles: '90d', // Manter logs de segurança por mais tempo
      auditFile: 'logs/.audit-security.json'
    }),
    
    // Transport com rotação para todos os eventos de segurança
    new DailyRotateFile({
      filename: 'logs/security-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'info', 
      maxSize: '200m',
      maxFiles: '60d',
      auditFile: 'logs/.audit-security-all.json'
    }),
    
    ...(process.env.NODE_ENV === 'development' ? [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      })
    ] : [])
  ]
});

/**
 * 🚨 Função para logar tentativas de ataque ou comportamento suspeito
 */
export const logSecurityEvent = (event: {
  type: 'rate_limit' | 'validation_failed' | 'auth_failed' | 'suspicious_activity' | 'xss_attempt';
  ip: string;
  userAgent?: string;
  userId?: string;
  details: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
}) => {
  securityLogger.warn('Security Event', {
    ...event,
    timestamp: new Date().toISOString()
  });
};

/**
 * 📋 Extensão do logger principal para incluir auditoria
 */
(logger as any).audit = (event: {
  type: string;
  severity: 'info' | 'warning' | 'error';
  ip?: string;
  details: any;
}) => {
  securityLogger.info('Audit Event', {
    ...event,
    timestamp: new Date().toISOString()
  });
};