{"name": "chatbot-prefeitura-frontend", "version": "1.0.0", "description": "Frontend para Sistema de Chatbot da Prefeitura", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.32.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.6.0", "lucide-react": "^0.372.0", "next": "^15.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.3", "recharts": "^2.12.6", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "@hookform/resolvers": "^3.3.4", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20.12.7", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.2.3", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.4.5"}}