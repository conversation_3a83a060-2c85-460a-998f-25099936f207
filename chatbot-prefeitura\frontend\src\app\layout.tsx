/**
 * 🏗️ Layout Principal da <PERSON>plicação
 * 
 * Layout raiz que configura provedores globais e estrutura básica
 */

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import React from 'react';
import '../styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Sistema Prefeitura Virtual',
  description: 'Sistema de gerenciamento municipal com chatbot inteligente',
  keywords: ['prefeitura', 'municipal', 'chatbot', 'gestão pública'],
  authors: [{ name: 'Prefeitura Virtual' }],
  viewport: 'width=device-width, initial-scale=1',
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="pt-BR">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#1f2937" />
        
        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      </head>
      
      <body className={`${inter.className} antialiased`}>
        {/* Provedor CSRF Global */}
        <CSRFProvider autoRefresh={true} refreshInterval={25 * 60 * 1000}>
          {/* Conteúdo principal */}
          <div className="min-h-screen bg-gray-50">
            {children}
          </div>
          
          {/* Status CSRF para desenvolvimento */}
          <CSRFStatus />
        </CSRFProvider>
        
        {/* Scripts de segurança */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Prevenir console access em produção
              if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
                console.log = console.warn = console.error = function() {};
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
