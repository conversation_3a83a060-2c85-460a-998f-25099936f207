/**
 * 🏠 Página Principal
 * 
 * Página inicial que demonstra o uso dos componentes
 * com proteção CSRF implementada.
 */

'use client';

import React, { useState } from 'react';
import LoginForm from '../components/Auth/LoginForm';
import ChatForm from '../components/Chat/ChatForm';
import { useCSRFStatus } from '../hooks/useCSRF';

interface User {
  id: string;
  nome: string;
  email?: string;
  cpf: string;
  secretaria: string;
  role: string;
}

export default function HomePage() {
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const csrfStatus = useCSRFStatus();

  /**
   * ✅ Lidar com login bem-sucedido
   */
  const handleLoginSuccess = (userData: any) => {
    setUser(userData.user);
    setError(null);
    console.log('Login realizado com sucesso:', userData);
  };

  /**
   * ❌ Lidar com erros
   */
  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    console.error('Erro:', errorMessage);
  };

  /**
   * 🚪 Fazer logout
   */
  const handleLogout = () => {
    setUser(null);
    setConversationId(null);
    setError(null);
  };

  /**
   * 💬 Lidar com mensagem enviada
   */
  const handleMessageSent = (message: any) => {
    console.log('Mensagem enviada:', message);
    
    // Gerar ID de conversa se não existir
    if (!conversationId) {
      setConversationId(`conv_${Date.now()}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">
                🏛️ Prefeitura Virtual
              </h1>
              <div className="ml-4 text-sm text-gray-500">
                Sistema de Gerenciamento Municipal
              </div>
            </div>
            
            {/* Status de segurança */}
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${
                csrfStatus.isValid 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  csrfStatus.isValid ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>
                  {csrfStatus.isValid ? 'Protegido' : 'Não protegido'}
                </span>
              </div>
              
              {user && (
                <button
                  onClick={handleLogout}
                  className="text-sm text-gray-600 hover:text-gray-900"
                >
                  Sair
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Conteúdo principal */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Erro global */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <div className="text-red-400 mr-3">⚠️</div>
              <div>
                <h3 className="text-sm font-medium text-red-800">Erro</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-600"
              >
                ✕
              </button>
            </div>
          </div>
        )}

        {!user ? (
          /* Tela de login */
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="mb-8 text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Bem-vindo ao Sistema
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl">
                Acesse o sistema de gerenciamento municipal com chatbot inteligente.
                Todas as operações são protegidas contra ataques CSRF.
              </p>
            </div>
            
            <LoginForm
              onSuccess={handleLoginSuccess}
              onError={handleError}
              className="w-full max-w-md"
            />
            
            {/* Informações de segurança */}
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg max-w-md">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                🛡️ Recursos de Segurança
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>✅ Proteção CSRF ativa</li>
                <li>✅ Validação de entrada robusta</li>
                <li>✅ Rate limiting implementado</li>
                <li>✅ Headers de segurança configurados</li>
                <li>✅ Sanitização XSS automática</li>
              </ul>
            </div>
          </div>
        ) : (
          /* Dashboard do usuário */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Informações do usuário */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  👤 Informações do Usuário
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Nome</label>
                    <p className="text-gray-900">{user.nome}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Secretaria</label>
                    <p className="text-gray-900 capitalize">{user.secretaria.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Função</label>
                    <p className="text-gray-900 capitalize">{user.role}</p>
                  </div>
                  {conversationId && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Conversa Ativa</label>
                      <p className="text-gray-900 font-mono text-xs">
                        {conversationId.substring(0, 16)}...
                      </p>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Status CSRF detalhado */}
              <div className="mt-6 bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  🛡️ Status de Segurança
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Token CSRF:</span>
                    <span className={csrfStatus.hasToken ? 'text-green-600' : 'text-red-600'}>
                      {csrfStatus.hasToken ? 'Presente' : 'Ausente'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Validade:</span>
                    <span className={csrfStatus.isValid ? 'text-green-600' : 'text-red-600'}>
                      {csrfStatus.isValid ? 'Válido' : 'Inválido'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Idade:</span>
                    <span className="text-gray-600">
                      {Math.floor(csrfStatus.age / 1000)}s
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Chat */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow h-[600px] flex flex-col">
                <div className="p-4 border-b">
                  <h3 className="text-lg font-medium text-gray-900">
                    💬 Chat com IA
                  </h3>
                  <p className="text-sm text-gray-600">
                    Converse com o assistente virtual da prefeitura
                  </p>
                </div>
                
                <ChatForm
                  conversationId={conversationId}
                  onMessageSent={handleMessageSent}
                  onError={handleError}
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        )}
      </main>
      
      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-500">
            <p>© 2024 Prefeitura Virtual. Sistema protegido contra ataques CSRF.</p>
            <p className="mt-1">
              Desenvolvido com segurança em mente. 
              {process.env.NODE_ENV === 'development' && (
                <span className="ml-2 text-blue-600">Modo de desenvolvimento ativo</span>
              )}
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
