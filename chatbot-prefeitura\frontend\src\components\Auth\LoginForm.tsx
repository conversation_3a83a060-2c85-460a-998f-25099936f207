/**
 * 🔐 Formulário de Login com Proteção CSRF
 * 
 * Componente de formulário de login que demonstra o uso correto
 * da proteção CSRF em formulários React.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useCSRF } from '../../hooks/useCSRF';
import apiService from '../../services/api';

interface LoginFormData {
  email: string;
  cpf: string;
  password: string;
  secretaria: string;
  loginType: 'email' | 'cpf';
}

interface LoginFormProps {
  onSuccess?: (userData: any) => void;
  onError?: (error: string) => void;
  className?: string;
}

const SECRETARIAS = [
  { value: 'administracao', label: 'Administração' },
  { value: 'financas', label: 'Finanças' },
  { value: 'saude', label: 'Saúde' },
  { value: 'educacao', label: 'Educação' },
  { value: 'obras', label: '<PERSON>bras e Urbanismo' },
  { value: 'assistencia', label: 'Assistência Social' },
  { value: 'meio_ambiente', label: 'Meio <PERSON>' }
];

export function LoginForm({ onSuccess, onError, className = '' }: LoginFormProps) {
  const { post, isLoading: csrfLoading, error: csrfError } = useCSRF();
  
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    cpf: '',
    password: '',
    secretaria: '',
    loginType: 'email'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  // Limpar erros quando os dados mudam
  useEffect(() => {
    if (formError) setFormError(null);
    if (Object.keys(fieldErrors).length > 0) setFieldErrors({});
  }, [formData]);

  // Notificar erro CSRF
  useEffect(() => {
    if (csrfError) {
      onError?.(csrfError);
    }
  }, [csrfError, onError]);

  /**
   * 📝 Atualizar dados do formulário
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  /**
   * 🔄 Alternar tipo de login
   */
  const handleLoginTypeChange = (type: 'email' | 'cpf') => {
    setFormData(prev => ({
      ...prev,
      loginType: type,
      email: '',
      cpf: ''
    }));
  };

  /**
   * ✅ Validar formulário
   */
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (formData.loginType === 'email') {
      if (!formData.email) {
        errors.email = 'Email é obrigatório';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        errors.email = 'Email inválido';
      }
    } else {
      if (!formData.cpf) {
        errors.cpf = 'CPF é obrigatório';
      } else if (!/^\d{11}$/.test(formData.cpf.replace(/\D/g, ''))) {
        errors.cpf = 'CPF deve ter 11 dígitos';
      }
    }

    if (!formData.password) {
      errors.password = 'Senha é obrigatória';
    } else if (formData.password.length < 8) {
      errors.password = 'Senha deve ter pelo menos 8 caracteres';
    }

    if (!formData.secretaria) {
      errors.secretaria = 'Secretaria é obrigatória';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  /**
   * 📤 Enviar formulário
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setFormError(null);

    try {
      // Preparar dados para envio
      const loginData = {
        password: formData.password,
        secretaria: formData.secretaria,
        ...(formData.loginType === 'email' 
          ? { email: formData.email }
          : { cpf: formData.cpf.replace(/\D/g, '') }
        )
      };

      // Fazer login usando o serviço com CSRF
      const response = await apiService.login(loginData);

      if (response.status === 'success') {
        onSuccess?.(response.data);
      } else {
        throw new Error(response.message || 'Erro no login');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setFormError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = isSubmitting || csrfLoading;

  return (
    <div className={`max-w-md mx-auto bg-white p-6 rounded-lg shadow-md ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Login</h2>
        <p className="text-gray-600">Acesse o sistema da prefeitura</p>
      </div>

      {/* Indicador de proteção CSRF */}
      <div className="mb-4 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
        🛡️ Formulário protegido contra CSRF
      </div>

      {/* Erro geral */}
      {formError && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-red-700">
          {formError}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Tipo de login */}
        <div className="flex space-x-4 mb-4">
          <button
            type="button"
            onClick={() => handleLoginTypeChange('email')}
            className={`px-4 py-2 rounded ${
              formData.loginType === 'email'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Email
          </button>
          <button
            type="button"
            onClick={() => handleLoginTypeChange('cpf')}
            className={`px-4 py-2 rounded ${
              formData.loginType === 'cpf'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            CPF
          </button>
        </div>

        {/* Campo de email ou CPF */}
        {formData.loginType === 'email' ? (
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                fieldErrors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
              disabled={isLoading}
            />
            {fieldErrors.email && (
              <p className="mt-1 text-sm text-red-600">{fieldErrors.email}</p>
            )}
          </div>
        ) : (
          <div>
            <label htmlFor="cpf" className="block text-sm font-medium text-gray-700 mb-1">
              CPF
            </label>
            <input
              type="text"
              id="cpf"
              name="cpf"
              value={formData.cpf}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                fieldErrors.cpf ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="000.000.000-00"
              disabled={isLoading}
            />
            {fieldErrors.cpf && (
              <p className="mt-1 text-sm text-red-600">{fieldErrors.cpf}</p>
            )}
          </div>
        )}

        {/* Senha */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            Senha
          </label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              fieldErrors.password ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Sua senha"
            disabled={isLoading}
          />
          {fieldErrors.password && (
            <p className="mt-1 text-sm text-red-600">{fieldErrors.password}</p>
          )}
        </div>

        {/* Secretaria */}
        <div>
          <label htmlFor="secretaria" className="block text-sm font-medium text-gray-700 mb-1">
            Secretaria
          </label>
          <select
            id="secretaria"
            name="secretaria"
            value={formData.secretaria}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              fieldErrors.secretaria ? 'border-red-500' : 'border-gray-300'
            }`}
            disabled={isLoading}
          >
            <option value="">Selecione uma secretaria</option>
            {SECRETARIAS.map(secretaria => (
              <option key={secretaria.value} value={secretaria.value}>
                {secretaria.label}
              </option>
            ))}
          </select>
          {fieldErrors.secretaria && (
            <p className="mt-1 text-sm text-red-600">{fieldErrors.secretaria}</p>
          )}
        </div>

        {/* Botão de submit */}
        <button
          type="submit"
          disabled={isLoading}
          className={`w-full py-2 px-4 rounded-md font-medium ${
            isLoading
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500'
          } text-white transition-colors`}
        >
          {isLoading ? 'Entrando...' : 'Entrar'}
        </button>
      </form>

      {/* Status CSRF para desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-2 bg-gray-50 border rounded text-xs text-gray-600">
          <div className="font-semibold mb-1">Debug CSRF:</div>
          <div>Loading: {csrfLoading ? 'Sim' : 'Não'}</div>
          {csrfError && <div className="text-red-600">Erro: {csrfError}</div>}
        </div>
      )}
    </div>
  );
}

export default LoginForm;
