/**
 * 💬 Formulário de Chat com Proteção CSRF
 * 
 * Componente de chat que demonstra o uso da proteção CSRF
 * em formulários de envio de mensagens.
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useCSRFRequest } from '../../hooks/useCSRF';
import apiService from '../../services/api';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

interface ChatFormProps {
  conversationId?: string;
  onMessageSent?: (message: Message) => void;
  onError?: (error: string) => void;
  className?: string;
}

export function ChatForm({ 
  conversationId, 
  onMessageSent, 
  onError, 
  className = '' 
}: ChatFormProps) {
  const { post, isLoading: csrfLoading, error: csrfError } = useCSRFRequest();
  
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Notificar erro CSRF
  useEffect(() => {
    if (csrfError) {
      onError?.(csrfError);
    }
  }, [csrfError, onError]);

  /**
   * 📝 Atualizar mensagem e ajustar altura do textarea
   */
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  /**
   * ⌨️ Lidar com teclas especiais
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  /**
   * 📤 Enviar mensagem
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || isSubmitting || csrfLoading) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Criar mensagem do usuário
      const userMessage: Message = {
        id: Date.now().toString(),
        content: message.trim(),
        sender: 'user',
        timestamp: new Date()
      };

      // Adicionar mensagem do usuário à lista
      setMessages(prev => [...prev, userMessage]);
      onMessageSent?.(userMessage);

      // Preparar dados para envio
      const messageData = {
        message: message.trim(),
        conversationId,
        context: {
          priority: 'normal' as const
        }
      };

      // Enviar mensagem usando o serviço com CSRF
      const response = await apiService.sendMessage(messageData);

      if (response.status === 'success') {
        // Criar mensagem de resposta do bot
        const botMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: response.data?.response || 'Mensagem recebida com sucesso!',
          sender: 'bot',
          timestamp: new Date()
        };

        setMessages(prev => [...prev, botMessage]);
        onMessageSent?.(botMessage);
        
        // Limpar campo de mensagem
        setMessage('');
        
        // Reset textarea height
        if (textareaRef.current) {
          textareaRef.current.style.height = 'auto';
        }
      } else {
        throw new Error(response.message || 'Erro ao enviar mensagem');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      onError?.(errorMessage);
      
      // Remover mensagem do usuário em caso de erro
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = isSubmitting || csrfLoading;

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Área de mensagens */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="mb-2">💬</div>
            <p>Inicie uma conversa enviando uma mensagem</p>
            <div className="mt-2 text-xs text-green-600">
              🛡️ Chat protegido contra CSRF
            </div>
          </div>
        ) : (
          messages.map((msg) => (
            <div
              key={msg.id}
              className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  msg.sender === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-800 border'
                }`}
              >
                <p className="text-sm">{msg.content}</p>
                <p className={`text-xs mt-1 ${
                  msg.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {msg.timestamp.toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))
        )}
        
        {/* Indicador de carregamento */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-white border rounded-lg px-4 py-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Formulário de envio */}
      <div className="border-t bg-white p-4">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleMessageChange}
              onKeyDown={handleKeyDown}
              placeholder="Digite sua mensagem... (Enter para enviar, Shift+Enter para nova linha)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none min-h-[40px] max-h-32"
              disabled={isLoading}
              rows={1}
            />
            
            {/* Contador de caracteres */}
            <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
              <span>{message.length}/4000 caracteres</span>
              {csrfError && (
                <span className="text-red-500">Erro CSRF: {csrfError}</span>
              )}
            </div>
          </div>
          
          <button
            type="submit"
            disabled={isLoading || !message.trim() || message.length > 4000}
            className={`px-4 py-2 rounded-md font-medium transition-colors ${
              isLoading || !message.trim() || message.length > 4000
                ? 'bg-gray-300 cursor-not-allowed text-gray-500'
                : 'bg-blue-500 hover:bg-blue-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500'
            }`}
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              '📤'
            )}
          </button>
        </form>
        
        {/* Status de desenvolvimento */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-gray-50 border rounded text-xs text-gray-600">
            <div className="flex justify-between">
              <span>CSRF Status:</span>
              <span className={csrfLoading ? 'text-yellow-600' : 'text-green-600'}>
                {csrfLoading ? 'Carregando...' : 'Protegido'}
              </span>
            </div>
            {conversationId && (
              <div className="mt-1">
                <span>Conversa: {conversationId.substring(0, 8)}...</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default ChatForm;
