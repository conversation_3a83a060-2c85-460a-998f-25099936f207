/**
 * 🛡️ Contexto CSRF para React
 * 
 * Provedor de contexto que gerencia tokens CSRF globalmente na aplicação,
 * permitindo acesso fácil e consistente à proteção CSRF em todos os componentes.
 */

'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import csrfManager from '../lib/csrf';

interface CSRFContextType {
  token: string | null;
  isLoading: boolean;
  error: string | null;
  isValid: boolean;
  refreshToken: () => Promise<void>;
  clearError: () => void;
}

const CSRFContext = createContext<CSRFContextType | undefined>(undefined);

interface CSRFProviderProps {
  children: ReactNode;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

/**
 * 🏭 Provedor de contexto CSRF
 */
export function CSRFProvider({ 
  children, 
  autoRefresh = true, 
  refreshInterval = 25 * 60 * 1000 // 25 minutos
}: CSRFProviderProps) {
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState(false);

  /**
   * 🔄 Atualizar token CSRF
   */
  const refreshToken = async (forceRefresh: boolean = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const newToken = await csrfManager.getCSRFToken(forceRefresh);
      setToken(newToken);
      setIsValid(true);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao obter token CSRF';
      setError(errorMessage);
      setToken(null);
      setIsValid(false);
      console.error('Erro no contexto CSRF:', err);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 🧹 Limpar erro
   */
  const clearError = () => {
    setError(null);
  };

  // Obter token inicial
  useEffect(() => {
    refreshToken();
  }, []);

  // Auto-refresh do token
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      const cacheStatus = csrfManager.getCacheStatus();
      
      // Renovar token se estiver próximo do vencimento
      if (!cacheStatus.isValid || cacheStatus.age > refreshInterval) {
        refreshToken(true);
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  // Monitorar validade do token
  useEffect(() => {
    const statusInterval = setInterval(() => {
      const cacheStatus = csrfManager.getCacheStatus();
      
      if (cacheStatus.isValid !== isValid) {
        setIsValid(cacheStatus.isValid);
      }
    }, 60000); // Verificar a cada minuto

    return () => clearInterval(statusInterval);
  }, [isValid]);

  const contextValue: CSRFContextType = {
    token,
    isLoading,
    error,
    isValid,
    refreshToken: () => refreshToken(true),
    clearError
  };

  return (
    <CSRFContext.Provider value={contextValue}>
      {children}
    </CSRFContext.Provider>
  );
}

/**
 * 🪝 Hook para usar o contexto CSRF
 */
export function useCSRFContext(): CSRFContextType {
  const context = useContext(CSRFContext);
  
  if (context === undefined) {
    throw new Error('useCSRFContext deve ser usado dentro de um CSRFProvider');
  }
  
  return context;
}

/**
 * 🔍 Componente para exibir status do CSRF (desenvolvimento)
 */
export function CSRFStatus() {
  const { token, isLoading, error, isValid } = useCSRFContext();
  
  // Só mostrar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-3 rounded-lg text-xs max-w-xs">
      <div className="font-bold mb-1">🛡️ CSRF Status</div>
      <div className="space-y-1">
        <div className={`flex items-center gap-2 ${isValid ? 'text-green-400' : 'text-red-400'}`}>
          <div className={`w-2 h-2 rounded-full ${isValid ? 'bg-green-400' : 'bg-red-400'}`}></div>
          {isValid ? 'Protegido' : 'Não protegido'}
        </div>
        
        {isLoading && (
          <div className="text-yellow-400">🔄 Carregando token...</div>
        )}
        
        {error && (
          <div className="text-red-400">❌ {error}</div>
        )}
        
        {token && (
          <div className="text-gray-400">
            Token: {token.substring(0, 8)}...
          </div>
        )}
      </div>
    </div>
  );
}

export default CSRFProvider;
