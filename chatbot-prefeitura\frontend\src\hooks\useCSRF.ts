/**
 * 🪝 Hook React para Proteção CSRF
 * 
 * Hook personalizado que facilita o uso de tokens CSRF em componentes React,
 * fornecendo estado reativo e funções otimizadas para requisições seguras.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import csrfManager, { getCSRFToken, fetchWithCSRF } from '../lib/csrf';

interface CSRFState {
  token: string | null;
  isLoading: boolean;
  error: string | null;
  isValid: boolean;
}

interface CSRFHookReturn extends CSRFState {
  refreshToken: () => Promise<void>;
  clearError: () => void;
  fetchWithCSRF: (url: string, options?: RequestInit) => Promise<Response>;
  post: (url: string, data?: any) => Promise<Response>;
  put: (url: string, data?: any) => Promise<Response>;
  delete: (url: string) => Promise<Response>;
  get: (url: string) => Promise<Response>;
}

/**
 * 🛡️ Hook para gerenciar tokens CSRF
 */
export function useCSRF(): CSRFHookReturn {
  const [state, setState] = useState<CSRFState>({
    token: null,
    isLoading: false,
    error: null,
    isValid: false
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * 🔄 Obter token CSRF
   */
  const refreshToken = useCallback(async (forceRefresh: boolean = false) => {
    // Cancelar requisição anterior se existir
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const token = await getCSRFToken(forceRefresh);
      
      setState({
        token,
        isLoading: false,
        error: null,
        isValid: true
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      setState({
        token: null,
        isLoading: false,
        error: errorMessage,
        isValid: false
      });
    }
  }, []);

  /**
   * 🧹 Limpar erro
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  /**
   * 🌐 Wrapper para fetch com CSRF
   */
  const safeFetchWithCSRF = useCallback(async (url: string, options: RequestInit = {}) => {
    try {
      clearError();
      return await fetchWithCSRF(url, options);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro na requisição';
      setState(prev => ({ ...prev, error: errorMessage }));
      throw error;
    }
  }, [clearError]);

  /**
   * 📤 POST com CSRF
   */
  const post = useCallback(async (url: string, data?: any) => {
    return safeFetchWithCSRF(url, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }, [safeFetchWithCSRF]);

  /**
   * 🔄 PUT com CSRF
   */
  const put = useCallback(async (url: string, data?: any) => {
    return safeFetchWithCSRF(url, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }, [safeFetchWithCSRF]);

  /**
   * 🗑️ DELETE com CSRF
   */
  const deleteRequest = useCallback(async (url: string) => {
    return safeFetchWithCSRF(url, {
      method: 'DELETE'
    });
  }, [safeFetchWithCSRF]);

  /**
   * 📥 GET sem CSRF
   */
  const get = useCallback(async (url: string) => {
    return safeFetchWithCSRF(url, {
      method: 'GET'
    });
  }, [safeFetchWithCSRF]);

  // Obter token inicial ao montar o componente
  useEffect(() => {
    refreshToken();

    // Cleanup ao desmontar
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [refreshToken]);

  // Verificar validade do token periodicamente
  useEffect(() => {
    const interval = setInterval(() => {
      const cacheStatus = csrfManager.getCacheStatus();
      
      if (!cacheStatus.isValid && state.isValid) {
        setState(prev => ({ ...prev, isValid: false }));
      }
    }, 60000); // Verificar a cada minuto

    return () => clearInterval(interval);
  }, [state.isValid]);

  return {
    ...state,
    refreshToken: () => refreshToken(true),
    clearError,
    fetchWithCSRF: safeFetchWithCSRF,
    post,
    put,
    delete: deleteRequest,
    get
  };
}

/**
 * 🎯 Hook simplificado para requisições específicas
 */
export function useCSRFRequest() {
  const { post, put, delete: deleteRequest, get, isLoading, error } = useCSRF();

  return {
    post,
    put,
    delete: deleteRequest,
    get,
    isLoading,
    error
  };
}

/**
 * 🔒 Hook para verificar se CSRF está ativo
 */
export function useCSRFStatus() {
  const [status, setStatus] = useState({
    hasToken: false,
    isValid: false,
    age: 0
  });

  useEffect(() => {
    const updateStatus = () => {
      const cacheStatus = csrfManager.getCacheStatus();
      setStatus(cacheStatus);
    };

    updateStatus();
    const interval = setInterval(updateStatus, 30000); // Atualizar a cada 30 segundos

    return () => clearInterval(interval);
  }, []);

  return status;
}

export default useCSRF;
