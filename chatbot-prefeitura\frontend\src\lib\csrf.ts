/**
 * 🛡️ Utilitários para Proteção CSRF
 * 
 * Este módulo fornece funcionalidades para obter e usar tokens CSRF
 * em requisições para o backend, garantindo proteção contra ataques
 * Cross-Site Request Forgery.
 */

interface CSRFTokenResponse {
  status: 'success' | 'error';
  data?: {
    csrfToken: string;
    headerName: string;
  };
  message: string;
  code?: string;
}

interface CSRFConfig {
  baseURL: string;
  tokenEndpoint: string;
  headerName: string;
  retryAttempts: number;
  cacheTimeout: number;
}

class CSRFManager {
  private config: CSRFConfig;
  private tokenCache: {
    token: string | null;
    timestamp: number;
    headerName: string;
  };

  constructor() {
    this.config = {
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
      tokenEndpoint: '/api/csrf-token',
      headerName: 'x-csrf-token',
      retryAttempts: 3,
      cacheTimeout: 30 * 60 * 1000 // 30 minutos
    };

    this.tokenCache = {
      token: null,
      timestamp: 0,
      headerName: this.config.headerName
    };
  }

  /**
   * 🎫 Obter token CSRF do servidor
   */
  async getCSRFToken(forceRefresh: boolean = false): Promise<string> {
    try {
      // Verificar cache se não for refresh forçado
      if (!forceRefresh && this.isTokenCacheValid()) {
        return this.tokenCache.token!;
      }

      const response = await fetch(`${this.config.baseURL}${this.config.tokenEndpoint}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: CSRFTokenResponse = await response.json();

      if (data.status !== 'success' || !data.data?.csrfToken) {
        throw new Error(data.message || 'Falha ao obter token CSRF');
      }

      // Atualizar cache
      this.tokenCache = {
        token: data.data.csrfToken,
        timestamp: Date.now(),
        headerName: data.data.headerName || this.config.headerName
      };

      return data.data.csrfToken;
    } catch (error) {
      console.error('Erro ao obter token CSRF:', error);
      throw new Error('Falha ao obter token de segurança. Tente novamente.');
    }
  }

  /**
   * ✅ Verificar se o token em cache ainda é válido
   */
  private isTokenCacheValid(): boolean {
    return (
      this.tokenCache.token !== null &&
      (Date.now() - this.tokenCache.timestamp) < this.config.cacheTimeout
    );
  }

  /**
   * 🔄 Limpar cache do token
   */
  clearTokenCache(): void {
    this.tokenCache = {
      token: null,
      timestamp: 0,
      headerName: this.config.headerName
    };
  }

  /**
   * 🌐 Cliente fetch com proteção CSRF automática
   */
  async fetchWithCSRF(url: string, options: RequestInit = {}): Promise<Response> {
    const fullUrl = url.startsWith('http') ? url : `${this.config.baseURL}${url}`;
    
    // Métodos que precisam de CSRF
    const methodsRequiringCSRF = ['POST', 'PUT', 'DELETE', 'PATCH'];
    const method = (options.method || 'GET').toUpperCase();
    
    let headers = new Headers(options.headers);
    
    // Adicionar token CSRF se necessário
    if (methodsRequiringCSRF.includes(method)) {
      try {
        const csrfToken = await this.getCSRFToken();
        headers.set(this.tokenCache.headerName, csrfToken);
      } catch (error) {
        console.error('Falha ao obter token CSRF:', error);
        throw error;
      }
    }

    // Configurar headers padrão
    if (!headers.has('Content-Type') && method !== 'GET') {
      headers.set('Content-Type', 'application/json');
    }

    const requestOptions: RequestInit = {
      ...options,
      method,
      headers,
      credentials: 'include'
    };

    try {
      const response = await fetch(fullUrl, requestOptions);

      // Se receber 403 (possível token CSRF inválido), tentar uma vez com token novo
      if (response.status === 403 && methodsRequiringCSRF.includes(method)) {
        console.warn('Token CSRF pode estar inválido, tentando renovar...');
        
        try {
          const newToken = await this.getCSRFToken(true);
          headers.set(this.tokenCache.headerName, newToken);
          
          const retryResponse = await fetch(fullUrl, {
            ...requestOptions,
            headers
          });
          
          return retryResponse;
        } catch (retryError) {
          console.error('Falha na tentativa de renovação do token CSRF:', retryError);
          return response; // Retornar resposta original
        }
      }

      return response;
    } catch (error) {
      console.error('Erro na requisição com CSRF:', error);
      throw error;
    }
  }

  /**
   * 📤 Wrapper para requisições POST com CSRF
   */
  async post(url: string, data?: any, options: RequestInit = {}): Promise<Response> {
    return this.fetchWithCSRF(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * 🔄 Wrapper para requisições PUT com CSRF
   */
  async put(url: string, data?: any, options: RequestInit = {}): Promise<Response> {
    return this.fetchWithCSRF(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * 🗑️ Wrapper para requisições DELETE com CSRF
   */
  async delete(url: string, options: RequestInit = {}): Promise<Response> {
    return this.fetchWithCSRF(url, {
      ...options,
      method: 'DELETE'
    });
  }

  /**
   * 📥 Wrapper para requisições GET (sem CSRF)
   */
  async get(url: string, options: RequestInit = {}): Promise<Response> {
    return this.fetchWithCSRF(url, {
      ...options,
      method: 'GET'
    });
  }

  /**
   * ⚙️ Configurar URL base
   */
  setBaseURL(baseURL: string): void {
    this.config.baseURL = baseURL;
  }

  /**
   * 📊 Obter status do cache
   */
  getCacheStatus(): { hasToken: boolean; isValid: boolean; age: number } {
    return {
      hasToken: this.tokenCache.token !== null,
      isValid: this.isTokenCacheValid(),
      age: Date.now() - this.tokenCache.timestamp
    };
  }
}

// Instância singleton
const csrfManager = new CSRFManager();

// Exportar funções principais
export const getCSRFToken = csrfManager.getCSRFToken.bind(csrfManager);
export const fetchWithCSRF = csrfManager.fetchWithCSRF.bind(csrfManager);
export const clearCSRFCache = csrfManager.clearTokenCache.bind(csrfManager);

// Exportar wrappers HTTP
export const csrfPost = csrfManager.post.bind(csrfManager);
export const csrfPut = csrfManager.put.bind(csrfManager);
export const csrfDelete = csrfManager.delete.bind(csrfManager);
export const csrfGet = csrfManager.get.bind(csrfManager);

// Exportar instância para uso avançado
export default csrfManager;
