/**
 * 🌐 Serviço de API com Proteção CSRF
 * 
 * Serviço centralizado para todas as requisições da aplicação,
 * com proteção CSRF automática e tratamento de erros padronizado.
 */

import csrfManager from '../lib/csrf';

interface ApiResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  message: string;
  code?: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

interface LoginData {
  email?: string;
  cpf?: string;
  password: string;
  secretaria: string;
}

interface MessageData {
  message: string;
  conversationId?: string;
  context?: {
    department?: string;
    priority: 'low' | 'normal' | 'high' | 'urgent';
    tags?: string[];
  };
}

interface ChangePasswordData {
  oldPassword: string;
  newPassword: string;
}

class ApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
  }

  /**
   * 🔧 Processar resposta da API
   */
  private async processResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }
      
      return data;
    } else {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return response as unknown as T;
    }
  }

  /**
   * 🔐 Autenticação - Login
   */
  async login(loginData: LoginData): Promise<ApiResponse> {
    try {
      const response = await csrfManager.post('/api/auth/login', loginData);
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro no login:', error);
      throw error;
    }
  }

  /**
   * 🚪 Autenticação - Logout
   */
  async logout(): Promise<ApiResponse> {
    try {
      const response = await csrfManager.post('/api/auth/logout');
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro no logout:', error);
      throw error;
    }
  }

  /**
   * 👤 Obter informações do usuário atual
   */
  async getCurrentUser(): Promise<ApiResponse> {
    try {
      const response = await csrfManager.get('/api/auth/me');
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro ao obter usuário atual:', error);
      throw error;
    }
  }

  /**
   * 🔑 Alterar senha
   */
  async changePassword(passwordData: ChangePasswordData): Promise<ApiResponse> {
    try {
      const response = await csrfManager.post('/api/auth/change-password', passwordData);
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro ao alterar senha:', error);
      throw error;
    }
  }

  /**
   * 💬 Enviar mensagem no chat
   */
  async sendMessage(messageData: MessageData): Promise<ApiResponse> {
    try {
      const response = await csrfManager.post('/api/chat/message', messageData);
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  /**
   * 📜 Obter histórico de conversa
   */
  async getChatHistory(conversationId?: string, page: number = 1, limit: number = 50): Promise<ApiResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      const url = conversationId 
        ? `/api/chat/history/${conversationId}?${params}`
        : `/api/chat/history?${params}`;

      const response = await csrfManager.get(url);
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro ao obter histórico:', error);
      throw error;
    }
  }

  /**
   * 📊 Obter métricas do dashboard
   */
  async getDashboardMetrics(period: string = 'month'): Promise<ApiResponse> {
    try {
      const params = new URLSearchParams({ period });
      const response = await csrfManager.get(`/api/dashboard/metrics?${params}`);
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro ao obter métricas:', error);
      throw error;
    }
  }

  /**
   * 📋 Obter atividades recentes
   */
  async getRecentActivities(page: number = 1, limit: number = 20): Promise<ApiResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });
      
      const response = await csrfManager.get(`/api/dashboard/activities?${params}`);
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro ao obter atividades:', error);
      throw error;
    }
  }

  /**
   * 🔄 Renovar token de acesso
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse> {
    try {
      const response = await csrfManager.post('/api/auth/refresh-token', { refreshToken });
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro ao renovar token:', error);
      throw error;
    }
  }

  /**
   * 🏥 Verificar saúde da API
   */
  async healthCheck(): Promise<ApiResponse> {
    try {
      const response = await csrfManager.get('/health');
      return this.processResponse(response);
    } catch (error) {
      console.error('Erro no health check:', error);
      throw error;
    }
  }

  /**
   * 🛡️ Obter token CSRF (para uso manual se necessário)
   */
  async getCSRFToken(): Promise<string> {
    try {
      return await csrfManager.getCSRFToken();
    } catch (error) {
      console.error('Erro ao obter token CSRF:', error);
      throw error;
    }
  }

  /**
   * ⚙️ Configurar URL base
   */
  setBaseURL(baseURL: string): void {
    this.baseURL = baseURL;
    csrfManager.setBaseURL(baseURL);
  }
}

// Instância singleton
const apiService = new ApiService();

export default apiService;
export type { ApiResponse, LoginData, MessageData, ChangePasswordData };
