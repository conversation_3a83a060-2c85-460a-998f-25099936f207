/**
 * 🎨 Extrator de Cores da Logo
 * Analisa imagens e extrai paleta de cores para tema personalizado
 */

interface ColorPalette {
  primary: string
  secondary: string  
  accent: string
  neutral: string
  success: string
  warning: string
  error: string
}

interface ExtractedColors {
  dominant: string[]
  palette: ColorPalette
  cssVariables: Record<string, string>
}

/**
 * 🔍 Extrai cores dominantes de uma imagem
 */
export async function extractColorsFromImage(imageSrc: string): Promise<ExtractedColors> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        if (!ctx) {
          throw new Error('Não foi possível obter contexto do canvas')
        }

        // Redimensionar para performance
        const maxSize = 200
        const ratio = Math.min(maxSize / img.width, maxSize / img.height)
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio
        
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        const colors = extractDominantColors(imageData.data)
        const palette = generatePalette(colors)
        const cssVariables = generateCSSVariables(palette)
        
        resolve({
          dominant: colors,
          palette,
          cssVariables
        })
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = () => reject(new Error('Erro ao carregar imagem'))
    img.src = imageSrc
  })
}

/**
 * 🎯 Extrai cores dominantes dos dados da imagem
 */
function extractDominantColors(data: Uint8ClampedArray, maxColors: number = 5): string[] {
  const colorCount: Record<string, number> = {}
  
  // Amostragem de pixels (pular alguns para performance)
  for (let i = 0; i < data.length; i += 16) { // 4 * 4 = amostragem reduzida
    const r = data[i]
    const g = data[i + 1]
    const b = data[i + 2]
    const a = data[i + 3]
    
    // Pular pixels transparentes
    if (a < 128) continue
    
    // Quantização de cores (agrupa cores similares)
    const quantizedR = Math.floor(r / 32) * 32
    const quantizedG = Math.floor(g / 32) * 32 
    const quantizedB = Math.floor(b / 32) * 32
    
    const color = `rgb(${quantizedR}, ${quantizedG}, ${quantizedB})`
    colorCount[color] = (colorCount[color] || 0) + 1
  }
  
  // Ordenar por frequência e retornar as mais dominantes
  return Object.entries(colorCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, maxColors)
    .map(([color]) => color)
}

/**
 * 🎨 Gera paleta temática baseada nas cores dominantes
 */
function generatePalette(dominantColors: string[]): ColorPalette {
  const [primary, secondary, accent] = dominantColors.slice(0, 3)
  
  return {
    primary: primary || 'rgb(59, 130, 246)', // Blue fallback
    secondary: secondary || 'rgb(16, 185, 129)', // Green fallback
    accent: accent || 'rgb(245, 101, 101)', // Red fallback
    neutral: 'rgb(107, 114, 128)', // Gray
    success: 'rgb(34, 197, 94)', // Green
    warning: 'rgb(251, 191, 36)', // Yellow
    error: 'rgb(239, 68, 68)' // Red
  }
}

/**
 * 📝 Gera CSS Variables a partir da paleta
 */
function generateCSSVariables(palette: ColorPalette): Record<string, string> {
  const rgbToHsl = (rgb: string): string => {
    const match = rgb.match(/rgb\((\d+), (\d+), (\d+)\)/)
    if (!match) return '0 0% 0%'
    
    const [, r, g, b] = match.map(Number)
    const [h, s, l] = rgbToHslValues(r, g, b)
    return `${h} ${s}% ${l}%`
  }

  const generateShades = (baseColor: string) => {
    const hsl = rgbToHsl(baseColor)
    const [h, s] = hsl.split(' ')
    
    return {
      '50': `${h} ${s} 97%`,
      '100': `${h} ${s} 94%`,
      '200': `${h} ${s} 87%`,
      '300': `${h} ${s} 77%`,
      '400': `${h} ${s} 66%`,
      '500': hsl, // Base color
      '600': `${h} ${s} 45%`,
      '700': `${h} ${s} 36%`,
      '800': `${h} ${s} 28%`,
      '900': `${h} ${s} 19%`,
      '950': `${h} ${s} 10%`
    }
  }

  const primaryShades = generateShades(palette.primary)
  const secondaryShades = generateShades(palette.secondary)

  return {
    // Cores base shadcn/ui
    '--background': '0 0% 100%',
    '--foreground': '222.2 84% 4.9%',
    '--card': '0 0% 100%',
    '--card-foreground': '222.2 84% 4.9%',
    '--popover': '0 0% 100%',
    '--popover-foreground': '222.2 84% 4.9%',
    
    // Cores primárias extraídas
    '--primary': primaryShades['500'],
    '--primary-foreground': '210 40% 98%',
    '--secondary': secondaryShades['100'],
    '--secondary-foreground': '222.2 47.4% 11.2%',
    
    // Cores de sistema
    '--muted': secondaryShades['100'],
    '--muted-foreground': '215.4 16.3% 46.9%',
    '--accent': rgbToHsl(palette.accent),
    '--accent-foreground': '222.2 47.4% 11.2%',
    
    '--destructive': rgbToHsl(palette.error),
    '--destructive-foreground': '210 40% 98%',
    '--border': secondaryShades['200'],
    '--input': secondaryShades['200'],
    '--ring': primaryShades['500'],
    '--radius': '0.5rem',
    
    // Modo escuro
    '--dark-background': '222.2 84% 4.9%',
    '--dark-foreground': '210 40% 98%',
    '--dark-primary': primaryShades['400'],
    '--dark-secondary': secondaryShades['800']
  }
}

/**
 * 🔧 Converte RGB para HSL
 */
function rgbToHslValues(r: number, g: number, b: number): [number, number, number] {
  r /= 255
  g /= 255
  b /= 255
  
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h: number, s: number, l = (max + min) / 2
  
  if (max === min) {
    h = s = 0 // achromatic
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
      default: h = 0
    }
    h /= 6
  }
  
  return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)]
}

/**
 * ✨ Aplica CSS Variables extraídas ao documento
 */
export function applyCSSVariables(variables: Record<string, string>) {
  const root = document.documentElement
  
  Object.entries(variables).forEach(([key, value]) => {
    root.style.setProperty(key, value)
  })
}

/**
 * 💾 Salva paleta extraída no localStorage
 */
export function saveExtractedTheme(colors: ExtractedColors) {
  localStorage.setItem('extracted-theme', JSON.stringify(colors))
}

/**
 * 📖 Carrega paleta salva do localStorage
 */
export function loadSavedTheme(): ExtractedColors | null {
  const saved = localStorage.getItem('extracted-theme')
  return saved ? JSON.parse(saved) : null
}

/**
 * 🎯 Função principal para extrair e aplicar tema da logo
 */
export async function extractAndApplyThemeFromLogo(logoPath: string): Promise<ExtractedColors> {
  try {
    // Log apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log('🎨 Extraindo cores da logo:', logoPath)
    }

    const extractedColors = await extractColorsFromImage(logoPath)

    // Log apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Cores extraídas:', extractedColors.dominant)
      console.log('🎨 Paleta gerada:', extractedColors.palette)
    }
    
    // Aplicar variáveis CSS
    applyCSSVariables(extractedColors.cssVariables)
    
    // Salvar tema
    saveExtractedTheme(extractedColors)
    
    return extractedColors
  } catch (error) {
    // Log de erro sempre ativo para debugging
    console.error('❌ Erro ao extrair cores:', error)
    throw error
  }
}