{"mcpServers": {"postgres-prefeitura": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres", "*****************************************/prefeituravirtual"], "description": "PostgreSQL MCP para explorar dados estruturados da Prefeitura de Valparaíso", "transport": "stdio"}, "mongodb-prefeitura": {"command": "npx", "args": ["mongo-mcp", "********************************************************************"], "description": "MongoDB MCP para dados de conversas e logs do chatbot de Valparaíso", "transport": "stdio"}}, "version": "1.0", "description": "MCPs configurados para desenvolvimento do chatbot da Prefeitura de Valparaíso", "usage": {"postgres": ["Liste todas as tabelas do PostgreSQL", "Descreva a estrutura da tabela [nome]", "Quantos registros tem a tabela [nome]?", "Mostre os relacionamentos entre tabelas"], "mongodb": ["Liste as coleções do MongoDB", "Quantos documentos tem cada coleção?", "Mostre um documento de exemplo da coleção [nome]"]}}