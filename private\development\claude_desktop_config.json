{"mcpServers": {"postgres-prefeitura": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "*****************************************/prefeituravirtual"], "env": {"POSTGRES_READ_ONLY": "true", "POSTGRES_MAX_ROWS": "1000"}}, "mongodb-prefeitura": {"command": "npx", "args": ["-y", "mongodb-mcp-server", "--connection-string", "********************************************************************"], "env": {"MONGODB_READ_ONLY": "true", "MONGODB_MAX_DOCUMENTS": "500"}}}, "globalShortcut": "Ctrl+Shift+Space"}