// Analisar apenas tabelas com dados reais
const { Client } = require('pg');

async function analyzeActiveTables() {
  console.log('🎯 ANALISANDO TABELAS COM DADOS REAIS\n');
  
  const client = new Client({
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'prefeituravirtual'
  });

  try {
    await client.connect();
    console.log('✅ Conectado!\n');

    // Tabelas que sabemos que têm dados
    const activeTables = [
      'cnaes',
      'menu',
      'menu_acao', 
      'cnae_grau_risco',
      'diario_oficial_tipo_documento',
      'formulario_validacoes',
      'gestao_departamento_parametros',
      'protocolo_virtual_situacaos',
      'protocolo_virtual_interessados'
    ];

    for (const table of activeTables) {
      await analyzeTableDetailed(client, table);
    }

    // Buscar outras tabelas com dados
    console.log('\n🔍 BUSCANDO OUTRAS TABELAS COM DADOS...\n');
    await findMoreActiveTables(client);

    // Analisar sistema de usuários (mesmo se vazio)
    console.log('\n👥 SISTEMA DE USUÁRIOS (estrutura):\n');
    await analyzeUserSystem(client);

  } catch (error) {
    console.error('❌ Erro:', error.message);
  } finally {
    await client.end();
  }
}

async function analyzeTableDetailed(client, tableName) {
  console.log(`📊 ${tableName.toUpperCase()}`);
  console.log('-'.repeat(50));
  
  try {
    // Contagem
    const countResult = await client.query(`SELECT COUNT(*) FROM "${tableName}"`);
    const count = parseInt(countResult.rows[0].count);
    console.log(`Registros: ${count.toLocaleString()}`);

    // Estrutura completa
    const columnsResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
      FROM information_schema.columns
      WHERE table_name = $1
      ORDER BY ordinal_position
    `, [tableName]);

    console.log(`Colunas: ${columnsResult.rows.length}`);
    columnsResult.rows.forEach((col, i) => {
      const nullable = col.is_nullable === 'YES' ? '?' : '';
      const maxLen = col.character_maximum_length ? `(${col.character_maximum_length})` : '';
      console.log(`  ${(i+1).toString().padStart(2)}. ${col.column_name.padEnd(25)} ${col.data_type}${maxLen}${nullable}`);
    });

    // Amostra dos dados (primeiras 3 linhas)
    if (count > 0 && count < 10000) {
      const sampleResult = await client.query(`SELECT * FROM "${tableName}" LIMIT 3`);
      console.log('\nAmostra dos dados:');
      sampleResult.rows.forEach((row, i) => {
        console.log(`  Linha ${i+1}:`);
        Object.keys(row).slice(0, 5).forEach(key => {
          let value = row[key];
          if (value !== null) {
            if (typeof value === 'string' && value.length > 50) {
              value = value.substring(0, 50) + '...';
            }
            console.log(`    ${key}: ${value}`);
          }
        });
      });
    }

    console.log('\n');
    
  } catch (error) {
    console.log(`❌ Erro: ${error.message}\n`);
  }
}

async function findMoreActiveTables(client) {
  // Buscar por tabelas que podem ter dados relevantes
  const possibleTables = [
    'users', 'user',
    'parametros',
    'perfil_acesso',
    'grupo_acessos',
    'notificacao',
    'gestao',
    'tokens',
    'sessions'
  ];

  for (const table of possibleTables) {
    try {
      const existsResult = await client.query(`
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = $1 AND table_schema = 'public'
        )
      `, [table]);

      if (existsResult.rows[0].exists) {
        const countResult = await client.query(`SELECT COUNT(*) FROM "${table}"`);
        const count = parseInt(countResult.rows[0].count);
        
        if (count > 0) {
          console.log(`✅ ${table}: ${count.toLocaleString()} registros`);
          
          // Mostrar estrutura básica
          const columnsResult = await client.query(`
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_name = $1
            ORDER BY ordinal_position
            LIMIT 8
          `, [table]);
          
          console.log('   Primeiras colunas:');
          columnsResult.rows.forEach(col => {
            console.log(`     - ${col.column_name} (${col.data_type})`);
          });
          console.log('');
        }
      }
    } catch (e) {
      // Ignorar erros
    }
  }
}

async function analyzeUserSystem(client) {
  const userTables = ['usuarios', 'users', 'departamentos', 'funcoes', 'perfil_acesso'];
  
  for (const table of userTables) {
    try {
      const existsResult = await client.query(`
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables 
          WHERE table_name = $1 AND table_schema = 'public'
        )
      `, [table]);

      if (existsResult.rows[0].exists) {
        const countResult = await client.query(`SELECT COUNT(*) FROM "${table}"`);
        const count = parseInt(countResult.rows[0].count);
        
        console.log(`📋 ${table}: ${count} registros`);
        
        // Mesmo sem dados, mostrar estrutura
        const columnsResult = await client.query(`
          SELECT column_name, data_type, is_nullable
          FROM information_schema.columns
          WHERE table_name = $1
          ORDER BY ordinal_position
        `, [table]);
        
        console.log('   Estrutura:');
        columnsResult.rows.slice(0, 8).forEach(col => {
          const nullable = col.is_nullable === 'YES' ? '?' : '';
          console.log(`     - ${col.column_name} (${col.data_type})${nullable}`);
        });
        console.log('');
      }
    } catch (e) {
      console.log(`❌ ${table}: erro ao verificar`);
    }
  }
}

analyzeActiveTables().catch(console.error);