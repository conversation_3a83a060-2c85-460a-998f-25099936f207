import dotenv from 'dotenv';
import { postgresService } from '../services/database/postgresql';
import { mongoService } from '../services/database/mongodb';
import { logger } from '../utils/logger';

dotenv.config();

async function mapPostgreSQL() {
  console.log('🐘 Mapeando estrutura PostgreSQL...\n');
  
  const isConnected = await postgresService.testConnection();
  if (!isConnected) {
    console.log('❌ Não foi possível conectar ao PostgreSQL');
    return;
  }

  const tables = await postgresService.listTables();
  
  console.log(`📋 Encontradas ${tables.length} tabelas:\n`);
  
  for (const table of tables.slice(0, 10)) { // Limitar a 10 tabelas para não sobrecarregar
    const info = await postgresService.getTableInfo(table);
    if (info) {
      console.log(`📊 Tabela: ${info.tableName}`);
      console.log(`   📈 Registros: ${info.recordCount}`);
      console.log(`   📝 Colunas: ${info.columns.length}`);
      
      // Mostrar algumas colunas principais
      const mainColumns = info.columns.slice(0, 5);
      mainColumns.forEach((col: any) => {
        console.log(`      - ${col.column_name} (${col.data_type})`);
      });
      console.log();
    }
  }

  if (tables.length > 10) {
    console.log(`... e mais ${tables.length - 10} tabelas\n`);
  }
}

async function mapMongoDB() {
  console.log('🍃 Mapeando estrutura MongoDB...\n');
  
  const isConnected = await mongoService.testConnection();
  if (!isConnected) {
    console.log('❌ Não foi possível conectar ao MongoDB');
    return;
  }

  const collections = await mongoService.listCollections();
  
  if (collections.length === 0) {
    console.log('📄 Nenhuma coleção encontrada (database vazio)');
    console.log('💡 Isso é normal para um projeto novo\n');
    return;
  }
  
  console.log(`📄 Encontradas ${collections.length} coleções:\n`);
  
  for (const collection of collections) {
    const info = await mongoService.getCollectionInfo(collection);
    if (info) {
      console.log(`📊 Coleção: ${info.collectionName}`);
      console.log(`   📈 Documentos: ${info.documentCount}`);
      
      if (info.sampleDocument) {
        const keys = Object.keys(info.sampleDocument).slice(0, 5);
        console.log(`   📝 Campos: ${keys.join(', ')}`);
      }
      console.log();
    }
  }
}

async function suggestImplementation() {
  console.log('💡 Sugestões para implementação:\n');
  
  // Buscar tabelas importantes
  const tables = await postgresService.listTables();
  const importantTables = ['usuarios', 'processos', 'secretarias', 'funcionarios', 'cidadaos', 'protocolos', 'orcamento'];
  
  const foundTables = tables.filter(table => 
    importantTables.some(important => 
      table.toLowerCase().includes(important) || important.includes(table.toLowerCase())
    )
  );

  if (foundTables.length > 0) {
    console.log('🎯 Tabelas relevantes encontradas:');
    for (const table of foundTables) {
      const info = await postgresService.getTableInfo(table);
      if (info) {
        console.log(`   ✓ ${table} (${info.recordCount} registros)`);
      }
    }
    console.log();
  }

  console.log('📋 Próximas implementações recomendadas:');
  console.log('1. Criar models Prisma baseados nas tabelas encontradas');
  console.log('2. Implementar queries específicas para cada secretaria');
  console.log('3. Integrar consultas com o chatbot DeepSeek');
  console.log('4. Criar endpoints de API para dashboard');
  console.log('5. Implementar sistema de cache para consultas frequentes');
  console.log();
  
  // Exemplo de consulta que o chatbot poderia fazer
  console.log('🤖 Exemplos de consultas para o chatbot:');
  console.log('   - "Quantos processos estão pendentes?"');
  console.log('   - "Listar funcionários da Secretaria de Saúde"');
  console.log('   - "Qual o orçamento executado este mês?"');
  console.log('   - "Buscar informações do cidadão João Silva"');
}

async function runMapping() {
  console.log('🔍 Iniciando mapeamento da estrutura dos bancos...\n');
  
  try {
    await mapPostgreSQL();
    await mapMongoDB();
    await suggestImplementation();
    
    console.log('✅ Mapeamento concluído com sucesso!');
    
  } catch (error) {
    logger.error('Erro durante mapeamento:', error);
    console.log('❌ Erro durante o mapeamento:', error);
  } finally {
    await postgresService.close();
    await mongoService.disconnect();
    process.exit(0);
  }
}

runMapping();