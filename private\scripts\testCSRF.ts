/**
 * 🧪 Script de Teste para Proteção CSRF
 * 
 * Script para testar se a proteção CSRF está funcionando corretamente,
 * simulando tanto requisições válidas quanto ataques CSRF.
 */

import fetch from 'node-fetch';
import { logger } from '../utils/logger';

interface TestResult {
  test: string;
  passed: boolean;
  message: string;
  details?: any;
}

class CSRFTester {
  private baseURL: string;
  private results: TestResult[] = [];

  constructor() {
    this.baseURL = process.env.API_URL || 'http://localhost:3001';
  }

  /**
   * 📊 Adicionar resultado de teste
   */
  private addResult(test: string, passed: boolean, message: string, details?: any): void {
    this.results.push({ test, passed, message, details });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${test}: ${message}`);
    
    if (details) {
      console.log(`   Detalhes:`, details);
    }
  }

  /**
   * 🎫 Obter token CSRF válido
   */
  private async getValidCSRFToken(): Promise<{ token: string; cookies: string[] }> {
    try {
      const response = await fetch(`${this.baseURL}/api/csrf-token`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const cookies = response.headers.raw()['set-cookie'] || [];

      return {
        token: data.data.csrfToken,
        cookies
      };
    } catch (error) {
      throw new Error(`Falha ao obter token CSRF: ${error.message}`);
    }
  }

  /**
   * 🍪 Extrair cookies de resposta
   */
  private extractCookies(cookieHeaders: string[]): string {
    return cookieHeaders.map(cookie => cookie.split(';')[0]).join('; ');
  }

  /**
   * 🧪 Teste 1: Obter token CSRF
   */
  async testGetCSRFToken(): Promise<void> {
    try {
      const { token, cookies } = await this.getValidCSRFToken();
      
      if (token && token.length > 0 && cookies.length > 0) {
        this.addResult(
          'Obter Token CSRF',
          true,
          'Token CSRF obtido com sucesso',
          { tokenLength: token.length, cookiesCount: cookies.length }
        );
      } else {
        this.addResult(
          'Obter Token CSRF',
          false,
          'Token ou cookies ausentes na resposta'
        );
      }
    } catch (error) {
      this.addResult(
        'Obter Token CSRF',
        false,
        `Erro ao obter token: ${error.message}`
      );
    }
  }

  /**
   * 🧪 Teste 2: Requisição POST sem token CSRF (deve falhar)
   */
  async testPostWithoutCSRF(): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpassword',
          secretaria: 'administracao'
        })
      });

      if (response.status === 403) {
        const data = await response.json();
        this.addResult(
          'POST sem CSRF',
          true,
          'Requisição bloqueada corretamente (403)',
          { code: data.code, message: data.message }
        );
      } else {
        this.addResult(
          'POST sem CSRF',
          false,
          `Requisição não foi bloqueada (status: ${response.status})`
        );
      }
    } catch (error) {
      this.addResult(
        'POST sem CSRF',
        false,
        `Erro no teste: ${error.message}`
      );
    }
  }

  /**
   * 🧪 Teste 3: Requisição POST com token CSRF inválido (deve falhar)
   */
  async testPostWithInvalidCSRF(): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': 'token-invalido-123'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpassword',
          secretaria: 'administracao'
        })
      });

      if (response.status === 403) {
        const data = await response.json();
        this.addResult(
          'POST com CSRF inválido',
          true,
          'Requisição bloqueada corretamente (403)',
          { code: data.code, message: data.message }
        );
      } else {
        this.addResult(
          'POST com CSRF inválido',
          false,
          `Requisição não foi bloqueada (status: ${response.status})`
        );
      }
    } catch (error) {
      this.addResult(
        'POST com CSRF inválido',
        false,
        `Erro no teste: ${error.message}`
      );
    }
  }

  /**
   * 🧪 Teste 4: Requisição POST com token CSRF válido (deve passar)
   */
  async testPostWithValidCSRF(): Promise<void> {
    try {
      const { token, cookies } = await this.getValidCSRFToken();
      const cookieString = this.extractCookies(cookies);

      const response = await fetch(`${this.baseURL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': token,
          'Cookie': cookieString
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpassword',
          secretaria: 'administracao'
        })
      });

      // Esperamos 400 ou 401 (credenciais inválidas), não 403 (CSRF)
      if (response.status !== 403) {
        this.addResult(
          'POST com CSRF válido',
          true,
          `Requisição passou pela proteção CSRF (status: ${response.status})`,
          { expectedNon403: true }
        );
      } else {
        const data = await response.json();
        this.addResult(
          'POST com CSRF válido',
          false,
          'Requisição foi bloqueada mesmo com token válido',
          { code: data.code, message: data.message }
        );
      }
    } catch (error) {
      this.addResult(
        'POST com CSRF válido',
        false,
        `Erro no teste: ${error.message}`
      );
    }
  }

  /**
   * 🧪 Teste 5: Requisição GET (não deve precisar de CSRF)
   */
  async testGetRequest(): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        this.addResult(
          'GET sem CSRF',
          true,
          'Requisição GET passou corretamente',
          { status: response.status }
        );
      } else {
        this.addResult(
          'GET sem CSRF',
          false,
          `Requisição GET falhou (status: ${response.status})`
        );
      }
    } catch (error) {
      this.addResult(
        'GET sem CSRF',
        false,
        `Erro no teste: ${error.message}`
      );
    }
  }

  /**
   * 🧪 Teste 6: Simulação de ataque CSRF cross-origin
   */
  async testCrossOriginAttack(): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/api/chat/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'https://malicious-site.com',
          'Referer': 'https://malicious-site.com/attack.html'
        },
        body: JSON.stringify({
          message: 'Tentativa de ataque CSRF'
        })
      });

      if (response.status === 403) {
        this.addResult(
          'Ataque Cross-Origin',
          true,
          'Ataque CSRF bloqueado corretamente',
          { status: response.status }
        );
      } else {
        this.addResult(
          'Ataque Cross-Origin',
          false,
          `Ataque não foi bloqueado (status: ${response.status})`
        );
      }
    } catch (error) {
      this.addResult(
        'Ataque Cross-Origin',
        false,
        `Erro no teste: ${error.message}`
      );
    }
  }

  /**
   * 🏃 Executar todos os testes
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Iniciando testes de proteção CSRF...\n');

    await this.testGetCSRFToken();
    await this.testGetRequest();
    await this.testPostWithoutCSRF();
    await this.testPostWithInvalidCSRF();
    await this.testPostWithValidCSRF();
    await this.testCrossOriginAttack();

    this.printSummary();
  }

  /**
   * 📊 Imprimir resumo dos testes
   */
  private printSummary(): void {
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);

    console.log('\n' + '='.repeat(60));
    console.log('📊 RESUMO DOS TESTES CSRF');
    console.log('='.repeat(60));
    console.log(`✅ Testes passaram: ${passed}/${total} (${percentage}%)`);
    console.log(`❌ Testes falharam: ${total - passed}/${total}`);

    if (passed === total) {
      console.log('\n🎉 Todos os testes passaram! Proteção CSRF está funcionando corretamente.');
    } else {
      console.log('\n⚠️  Alguns testes falharam. Verifique a implementação da proteção CSRF.');
      
      console.log('\n❌ Testes que falharam:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    console.log('\n🛡️ Status de segurança:');
    console.log(`   - Proteção CSRF: ${percentage >= 80 ? 'ATIVA' : 'FALHA'}`);
    console.log(`   - Nível de segurança: ${percentage >= 90 ? 'ALTO' : percentage >= 70 ? 'MÉDIO' : 'BAIXO'}`);
  }
}

// Executar testes se chamado diretamente
if (require.main === module) {
  const tester = new CSRFTester();
  tester.runAllTests().catch(error => {
    console.error('❌ Erro ao executar testes:', error);
    process.exit(1);
  });
}

export default CSRFTester;
