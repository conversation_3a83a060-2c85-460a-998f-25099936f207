import dotenv from 'dotenv';
import { Client } from 'pg';
import mongoose from 'mongoose';

dotenv.config();

async function testPostgreSQL() {
  console.log('🐘 Testando conexão PostgreSQL...');
  
  const client = new Client({
    host: process.env.POSTGRES_HOST,
    port: parseInt(process.env.POSTGRES_PORT || '5432'),
    user: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
  });

  try {
    await client.connect();
    console.log('✅ Conectado ao PostgreSQL!');
    
    // Listar tabelas
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
      LIMIT 20
    `);
    
    console.log('\n📋 Tabelas encontradas:');
    tablesResult.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    // Verificar se existem tabelas importantes
    const importantTables = ['usuarios', 'processos', 'secretarias', 'funcionarios'];
    for (const table of importantTables) {
      const exists = tablesResult.rows.some(row => row.table_name === table);
      if (exists) {
        const count = await client.query(`SELECT COUNT(*) FROM ${table}`);
        console.log(`\n  ✓ Tabela '${table}' existe com ${count.rows[0].count} registros`);
      }
    }
    
  } catch (error) {
    console.error('❌ Erro ao conectar PostgreSQL:', error);
  } finally {
    await client.end();
  }
}

async function testMongoDB() {
  console.log('\n🍃 Testando conexão MongoDB...');
  
  try {
    await mongoose.connect(process.env.MONGODB_URI!);
    console.log('✅ Conectado ao MongoDB!');
    
    // Listar coleções
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    
    console.log('\n📋 Coleções encontradas:');
    for (const collection of collections) {
      console.log(`  - ${collection.name}`);
      
      // Contar documentos
      const count = await db.collection(collection.name).countDocuments();
      console.log(`    (${count} documentos)`);
    }
    
  } catch (error) {
    console.error('❌ Erro ao conectar MongoDB:', error);
  } finally {
    await mongoose.disconnect();
  }
}

async function runTests() {
  console.log('🔍 Iniciando testes de conexão com bancos de dados...\n');
  
  await testPostgreSQL();
  await testMongoDB();
  
  console.log('\n✨ Testes concluídos!');
  console.log('\n💡 Próximos passos:');
  console.log('1. Analisar as tabelas encontradas');
  console.log('2. Criar models Prisma baseados nas tabelas existentes');
  console.log('3. Configurar schemas Mongoose para as coleções');
  console.log('4. Implementar queries para o chatbot');
}

runTests().catch(console.error);