import dotenv from 'dotenv';
import { deepseekService } from '../services/ai/deepseekService';

dotenv.config();

async function testDeepSeek() {
  console.log('🧪 Testando integração com DeepSeek...\n');

  try {
    // Teste 1: Mensagem simples
    console.log('Teste 1: Mensagem simples');
    const response1 = await deepseekService.processMessage({
      message: 'Ol<PERSON>, quais são os serviços disponíveis?',
      secretaria: 'administracao'
    });
    console.log('Resposta:', response1);
    console.log('\n---\n');

    // Teste 2: Consulta específica
    console.log('Teste 2: Consulta sobre processos');
    const response2 = await deepseekService.processMessage({
      message: 'Quantos processos estão em andamento na secretaria?',
      secretaria: 'financas'
    });
    console.log('Resposta:', response2);
    console.log('\n---\n');

    // Teste 3: Geração de SQL
    console.log('Teste 3: Geração de query SQL');
    const schema = `
    CREATE TABLE processos (
      id SERIAL PRIMARY KEY,
      numero VARCHAR(20),
      descricao TEXT,
      status VARCHAR(50),
      data_abertura DATE,
      secretaria VARCHAR(50)
    );`;
    
    const sqlQuery = await deepseekService.generateSQLQuery(
      'buscar todos os processos em andamento da secretaria de finanças',
      schema
    );
    console.log('Query SQL:', sqlQuery);

    console.log('\n✅ Todos os testes concluídos com sucesso!');
  } catch (error) {
    console.error('❌ Erro nos testes:', error);
  }
}

// Executar testes
testDeepSeek();