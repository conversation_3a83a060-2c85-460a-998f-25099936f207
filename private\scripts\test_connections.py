#!/usr/bin/env python3
import psycopg2
from pymongo import MongoClient
import sys
import os
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

def test_postgresql():
    print('🐘 Testando conexão PostgreSQL...')
    
    try:
        connection = psycopg2.connect(
            host=os.getenv('POSTGRES_HOST', '*************'),
            port=int(os.getenv('POSTGRES_PORT', '5411')),
            user=os.getenv('POSTGRES_USER', 'otto'),
            password=os.getenv('POSTGRES_PASSWORD', 'otto'),
            database=os.getenv('POSTGRES_DB', 'pv_valparaiso')
        )
        
        cursor = connection.cursor()
        print('✅ Conectado ao PostgreSQL!')
        
        # Listar tabelas
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
            LIMIT 20
        """)
        
        tables = cursor.fetchall()
        print('\n📋 Tabelas encontradas:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # Verificar tabelas importantes
        important_tables = ['usuarios', 'processos', 'secretarias', 'funcionarios', 'cidadaos', 'protocolos']
        for table in important_tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                count = cursor.fetchone()[0]
                print(f'  ✓ Tabela "{table}" com {count} registros')
            except psycopg2.Error:
                pass  # Tabela não existe
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f'❌ Erro PostgreSQL: {e}')
        return False
    
    return True

def test_mongodb():
    print('\n🍃 Testando conexão MongoDB...')
    
    try:
        client = MongoClient(os.getenv('MONGODB_URI', '*****************************************************************'))
        
        # Testar conexão
        client.admin.command('ping')
        print('✅ Conectado ao MongoDB!')
        
        # Listar databases
        databases = client.list_database_names()
        print(f'\n📋 Databases encontrados: {databases}')
        
        # Conectar ao database específico
        db = client['chatbot_prefeitura']
        collections = db.list_collection_names()
        
        if collections:
            print(f'\n📄 Coleções encontradas:')
            for collection in collections:
                count = db[collection].count_documents({})
                print(f'  - {collection} ({count} documentos)')
        else:
            print('\n📄 Nenhuma coleção encontrada (database vazio)')
        
        client.close()
        
    except Exception as e:
        print(f'❌ Erro MongoDB: {e}')
        return False
    
    return True

if __name__ == '__main__':
    print('🔍 Testando conexões com bancos de dados...\n')
    
    pg_success = test_postgresql()
    mongo_success = test_mongodb()
    
    print('\n✨ Resumo dos testes:')
    print(f'PostgreSQL: {"✅ Sucesso" if pg_success else "❌ Falha"}')
    print(f'MongoDB: {"✅ Sucesso" if mongo_success else "❌ Falha"}')
    
    if pg_success and mongo_success:
        print('\n🎉 Todas as conexões funcionando!')
        print('\n💡 Próximos passos:')
        print('1. Analisar as tabelas/coleções encontradas')
        print('2. Criar models baseados na estrutura existente')
        print('3. Implementar queries para o chatbot')
    else:
        print('\n⚠️ Algumas conexões falharam. Verifique as credenciais.')
        sys.exit(1)