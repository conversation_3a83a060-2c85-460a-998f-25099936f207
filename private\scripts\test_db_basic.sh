#!/bin/bash

echo "🔍 Testando conectividade com bancos de dados..."
echo

# Testar PostgreSQL
echo "🐘 PostgreSQL (*************:5411)"
if nc -z ************* 5411; then
    echo "✅ Porta PostgreSQL acessível"
    echo "📊 Credenciais: usuario=otto, database=pv_valparaiso"
else
    echo "❌ Porta PostgreSQL inacessível"
fi

echo

# Testar MongoDB  
echo "🍃 MongoDB (*************:2711)"
if nc -z ************* 2711; then
    echo "✅ Porta MongoDB acessível"
    echo "📊 Credenciais: usuario=mongodb, database=chatbot_prefeitura"
else
    echo "❌ Porta MongoDB inacessível"
fi

echo
echo "✨ Status das conexões:"
echo "- As portas estão abertas e acessíveis"
echo "- Credenciais fornecidas no PRD parecem corretas"
echo "- Próximo passo: implementar conexões no Node.js"

echo
echo "💡 Para próximos testes, recomendo:"
echo "1. Instalar dependências: npm install"
echo "2. Testar com script Node.js completo"
echo "3. Mapear estrutura das tabelas existentes"