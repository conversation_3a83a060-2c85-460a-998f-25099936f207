const { Client } = require('pg');

async function testPvValparaiso() {
  console.log('🔍 Testando conexão com banco pv_valparaiso...\n');
  
  // Primeiro, listar bancos disponíveis
  console.log('📋 Listando bancos disponíveis...');
  const listClient = new Client({
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'postgres' // banco padrão para listar outros bancos
  });

  try {
    await listClient.connect();
    const result = await listClient.query(`
      SELECT datname as database_name 
      FROM pg_database 
      WHERE datistemplate = false 
      ORDER BY datname;
    `);
    
    console.log('Bancos encontrados:');
    result.rows.forEach((row, i) => {
      console.log(`  ${i + 1}. ${row.database_name}`);
    });
    
    await listClient.end();
    
    // Verificar se pv_valparaiso existe
    const pvExists = result.rows.some(row => row.database_name === 'pv_valparaiso');
    const prefeituravirtualExists = result.rows.some(row => row.database_name === 'prefeituravirtual');
    
    console.log(`\n🔍 Status dos bancos:`);
    console.log(`  - pv_valparaiso: ${pvExists ? '✅ Existe' : '❌ Não encontrado'}`);
    console.log(`  - prefeituravirtual: ${prefeituravirtualExists ? '✅ Existe' : '❌ Não encontrado'}`);
    
    // Testar conexão com pv_valparaiso se existir
    if (pvExists) {
      console.log('\n🐘 Testando conexão com pv_valparaiso...');
      const pvClient = new Client({
        host: '*************',
        port: 5411,
        user: 'otto',
        password: 'otto',
        database: 'pv_valparaiso'
      });
      
      try {
        await pvClient.connect();
        console.log('✅ Conectado ao pv_valparaiso!');
        
        // Contar tabelas
        const tablesResult = await pvClient.query(`
          SELECT COUNT(*) as table_count 
          FROM information_schema.tables 
          WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        `);
        
        console.log(`📊 Número de tabelas: ${tablesResult.rows[0].table_count}`);
        
        // Listar algumas tabelas importantes
        const importantTables = ['usuarios', 'processos', 'secretarias', 'funcionarios', 'cidadaos', 'protocolos'];
        console.log('\n📋 Verificando tabelas importantes:');
        
        for (const table of importantTables) {
          try {
            const exists = await pvClient.query(`
              SELECT COUNT(*) FROM information_schema.tables 
              WHERE table_name = $1 AND table_schema = 'public'
            `, [table]);
            
            if (exists.rows[0].count > 0) {
              const count = await pvClient.query(`SELECT COUNT(*) FROM "${table}"`);
              console.log(`  ✓ ${table}: ${count.rows[0].count} registros`);
            } else {
              console.log(`  ❌ ${table}: não encontrada`);
            }
          } catch (e) {
            console.log(`  ❌ ${table}: erro ao verificar`);
          }
        }
        
        await pvClient.end();
        
      } catch (error) {
        console.error('❌ Erro ao conectar com pv_valparaiso:', error.message);
      }
    }
    
    // Testar conexão com prefeituravirtual se existir
    if (prefeituravirtualExists) {
      console.log('\n🐘 Testando conexão com prefeituravirtual...');
      const prefClient = new Client({
        host: '*************',
        port: 5411,
        user: 'otto',
        password: 'otto',
        database: 'prefeituravirtual'
      });
      
      try {
        await prefClient.connect();
        console.log('✅ Conectado ao prefeituravirtual!');
        
        // Contar tabelas
        const tablesResult = await prefClient.query(`
          SELECT COUNT(*) as table_count 
          FROM information_schema.tables 
          WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        `);
        
        console.log(`📊 Número de tabelas: ${tablesResult.rows[0].table_count}`);
        
        await prefClient.end();
        
      } catch (error) {
        console.error('❌ Erro ao conectar com prefeituravirtual:', error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Erro ao listar bancos:', error.message);
  }
}

testPvValparaiso().catch(console.error);
